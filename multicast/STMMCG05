//STMMCG05 JOB STMMCG05,'STMMCG05',MSGCLASS=X,CLASS=A,                  00010045
//     NOTIFY=&SYSUID                                                   00020000
//STEP01   EXEC IGYWCL                                                  00030000
//SYSPRINT DD   SYSOUT=X                                                00040000
//COBOL.SYSLIB DD DSN=&LIBPRFX..SCEESAMP,DISP=SHR                       00050000
//             DD DSN=LIBRBCM.USERACC.AP.BTCHSRC,DISP=SHR               00060091
//SYSIN    DD   *                                                       00070000
      *------------------------*                                        00080000
       IDENTIFICATION DIVISION.                                         00090000
      *------------------------*                                        00100000
       PROGRAM-ID.   STMMCG05.                                          ********
      *------------------------------------------------------------*    ********
      * PROGRAM NAME: STMMCG05                                          ********
      * AUTHOR      : ITTICHOTE CH.                                     ********
      * STARTED DATE: 15/02/2017                                        ********
      * DESCRIPTION : THIS PROGRAM WILL GENERATE                        ********
      *               INTERBANK STATEMENT WITH CN PAYMENT DETAIL        ********
      * R58080051   : 06/10/15 - NOT GENERATE STMT FOR "PAY??????SCG"   ********
      *                        - NOT GENERATE STMT FOR "VAL??????SCG"   ********
      * R59060043   : 12/09/16 - SUPPORT IPS DATA FILE                  ********
      * R59060091   : 15/09/16 - MULTI CASH SCG                         ********
      * CB60020006  : 10/04/17 - Gen Data Support MT940 DBMS            ********
      * CB61010008  : 15/02/18 - NOT GENERATE STMT FOR "PAY?????????"   ********
      *                        - NOT GENERATE STMT FOR "PA2?????????"   ********
      *                        - NOT GENERATE STMT FOR "PA3?????????"   ********
      *                        - NOT GENERATE STMT FOR "PA4?????????"   ********
      *                        - NOT GENERATE STMT FOR "PA5?????????"   ********
      *                        - NOT GENERATE STMT FOR "PA6?????????"   ********
      * CB62090005  : 20/09/19 - SUPPORT CHEQUE 8 DIGIT                 00179344
      *                    OLD => "DDP"+SPACE(1)+PDRJNNNNNNN            00179444
      *                           "DDP"+SPACE(1)+PDSTNNNNNNN            00179544
      *                           "MCP"+SPACE(1)+NNNNNNNPDRJ            00179644
      *                           "MCP"+SPACE(1)+NNNNNNNPDST            00179744
      *                    NEW => "DDP"+SPACE(1)+PDRJNNNNNNNN           00179944
      *                           "DDP"+SPACE(1)+PDSTNNNNNNNN           00180044
      *                           "MCP"+SPACE(1)+NNNNNNNNPDRJ           00180144
      *                           "MCP"+SPACE(1)+NNNNNNNNPDST           00180244
      * CB62090014  : 06/01/20 - Not Gen Statement Status 'C'           00180344
      *                          Status 'C' = Cancel Before Debit       00180444
      *                          Status 'J' = Cancel After  Debit       00180544
      * CB63060014  : 01/10/20 - Mapping Product code from DCP TO BNT   00180644
      *                           "DCP"+SPACE(1)+"H"+NNNN+"BNT"+NNNN    00180744
      * CB64010022  : 01/08/21 - Gen Statement for Product code "EWT"   00180846
      * SR-22493    : 31/08/23 - Change Logic for Product code "EWT"    00181286
      *                          Because New Stmt Desc Chang :          00181382
      *                          Old stmt = "EWT"+SPACE(1)+...          00181482
      *                          New stmt = SPACE(1)+"EWT"+SPACE(1)+... 00181582
      *------------------------------------------------------------*    00182044
       ENVIRONMENT DIVISION.                                            00190000
      *---------------------*                                           00200000
       CONFIGURATION SECTION.                                           00210000
       SOURCE-COMPUTER. IBM-4341.                                       00220000
       OBJECT-COMPUTER. IBM-4341.                                       00230000
       SPECIAL-NAMES.   C01 IS  NEXT-PAGE.                              ********
      *----------------------*                                          ********
       INPUT-OUTPUT SECTION.                                            ********
      *----------------------*                                          ********
       FILE-CONTROL.                                                    ********
                                                                        ********
      *** READ ERP ACCOUNT PROFILE (PS INPUT) FROM ERP                  ********
            SELECT ACCT-IN-FL ASSIGN    TO  ACCTINF                     ********
                   FILE STATUS          IS  ACCT-IN-STAT.               ********
                                                                        ********
      *** READ STATEMENT TRANSACTION (VSAM INPUT) FROM IM/ST            ********
            SELECT STMT-IN-FL ASSIGN    TO  STMTINF                     ********
                   ORGANIZATION         IS  INDEXED                     ********
                   ACCESS MODE          IS  SEQUENTIAL                  ********
                   RECORD KEY           IS  STMT-CNTL-REC               ********
                   FILE STATUS          IS  STMT-IN-STAT.               ********
                                                                        ********
      *** STATEMENT Detail DEBIT Transaction (VSAM File) from CN        ********
            SELECT DRBT-IO-FL ASSIGN    TO  DRBTIOF                     00420000
                   ORGANIZATION         IS  INDEXED                     00430000
                   ACCESS MODE          IS  SEQUENTIAL                  00440000
                   RECORD KEY           IS  DRBT-CNTL-REC               00450000
                   FILE STATUS          IS  DRBT-IO-STAT.               00460000
                                                                        00470000
      *** STATEMENT Detail CREDIT Transaction (VSAM File) from CN       00480000
            SELECT CRDT-IO-FL ASSIGN    TO  CRDTIOF                     00490000
                   ORGANIZATION         IS  INDEXED                     00500000
                   ACCESS MODE          IS  SEQUENTIAL                  00510000
                   RECORD KEY           IS  CRDT-CNTL-REC               00520000
                   FILE STATUS          IS  CRDT-IO-STAT.               00530000
                                                                        00540000
      *** WRITE ERP DAILY STATEMENT (PS OUTPUT) TO ERP                  00550000
            SELECT STMT-OUTD-FL ASSIGN   TO  STMTOUTD                   00560000
                   FILE STATUS          IS  STMT-OUTD-STAT.             00570000
                                                                        00580000
      *** WRITE ERP EXCEPTION REPORT (PS OUTPUT) TO ERP                 00590000
            SELECT XCPT-OUT-FL ASSIGN   TO  XCPTOUTF                    00600000
                   FILE STATUS          IS  XCPT-OUT-STAT.              00610000
                                                                        00620000
      *** THIS AREA IS DATA SORTING                                     00630000
            SELECT SORT-WORK ASSIGN TO SYS001-UT-3350-S-SORTWK1.        00640000
                                                                        00650000
      *--------------*                                                  00660000
       DATA DIVISION.                                                   00670000
      *--------------*                                                  00680000
       FILE SECTION.                                                    00690000
                                                                        00700000
       FD ACCT-IN-FL                                                    00710000
           LABEL  RECORDS    ARE STANDARD                               00720000
           RECORD CONTAINS  200 CHARACTERS                              00730000
           DATA   RECORD     IS ACCT-IN-REC.                            00740000
       01 ACCT-IN-REC.                                                  00750000
          05 ACCT-IN-CORP-ID        PIC  X(16).                         00752144
          05 ACCT-NO                PIC  X(10).                         00753044
          05 FILLER                 PIC  X(03).                         00754044
          05 ACCT-NAME              PIC  X(50).                         00755044
          05 ACCT-STMT1-DETAIL.                                         00755144
             10 ACCT-STMT1-FREQ  PIC  X(03).                            00755244
             10 ACCT-STMT1-EXT   PIC  X(20).                            00755344
          05 ACCT-STMT2-DETAIL.                                         00755444
             10 ACCT-STMT2-FREQ     PIC  X(03).                         00755544
             10 ACCT-STMT2-EXT.                                         00755644
                15 ACCT-PAYMENT-DET PIC  X(01).                         00755744
                15 ACCT-REPORT-GRP  PIC  X(10).                         00755844
                15 FILLER           PIC  X(09).                         00755944
          05 FILLER                 PIC  X(25).                         00756044
          05 ACCT-MCASH-FLG         PIC  X(01).                         00757044
          05 ACCT-PAYMENT-FLG       PIC  X(01).                         00758044
          05 ACCT-LCC-FLG           PIC  X(01).                         00759044
          05 ACCT-BPAY-FLG          PIC  X(01).                         00759144
          05 ACCT-EBPP-FLG          PIC  X(01).                         00759244
          05 FILLER                 PIC  X(45).                         00759344
                                                                        00900000
       FD STMT-IN-FL                                                    00910000
     ***   RECORD CONTAINS   1300 CHARACTERS                            00920044
           RECORD CONTAINS   2100 CHARACTERS                            00921044
           DATA   RECORD     IS STMT-IN-REC.                            00930000
       01 STMT-IN-REC.                                                  00940000
          05 STMT-CNTL-REC.                                             00950000
             10 STMT-KEY-IN-ACCT-NO    PIC  X(10).                      ********
             10 STMT-KEY-IN-SEQ        PIC  X(06).                      ********
          05 STMT-DETL-REC.                                             ********
             10 STMT-IN-REC-TYPE       PIC  X(01).                      ********
             10 FILLER                 PIC  X(05).                      ********
             10 STMT-IN-ACCT-NO        PIC  X(10).                      ********
             10 STMT-IN-BANK-ID        PIC  X(03).                      ********
             10 STMT-IN-BRNO           PIC  X(04).                      ********
             10 STMT-IN-TDATE.                                          ********
                15 STMT-IN-TDATE-YYYY  PIC  X(04).                      ********
                15 STMT-IN-TDATE-MM    PIC  X(02).                      ********
                15 STMT-IN-TDATE-DD    PIC  X(02).                      ********
             10 STMT-IN-BAMT           PIC  9(14)V99.                   ********
             10 STMT-IN-BAMT-SIGN      PIC  X(01).                      ********
             10 STMT-IN-TAMT           PIC  9(13)V99.                   ********
             10 STMT-IN-CRDR           PIC  X(02).                      ********
             10 STMT-IN-TCODE          PIC  X(04).                      ********
             10 STMT-IN-TERM           PIC  X(04).                      ********
             10 STMT-IN-CHQNO          PIC  X(07).                      01150000
             10 STMT-IN-REF-NUM        PIC  X(25).                      01160000
             10 FILLER                 PIC  X(02).                      01170000
             10 STMT-IN-GROUP-ID       PIC  X(05).                      01180000
             10 STMT-IN-SERIAL-NO      PIC  X(06).                      01190000
             10 STMT-IN-CHQNO-10       PIC  X(10).                      01200000
      *>> SR-22493                                                      01201089
      *      10 STMT-IN-DESC           PIC  X(20).                      01202089
      *      10 STMT-IN-DESC-0         PIC  X(20).                      01203089
             10 STMT-IN-DESC-A.                                         01210084
                15 STMT-IN-DESC           PIC  X(20).                   01211085
                15 STMT-IN-DESC-0         PIC  X(20).                   01220085
      *<< SR-22493                                                      01221089
             10 STMT-IN-SYMBOL         PIC  X(03).                      01230000
             10 STMT-IN-CHAN           PIC  X(04).                      01240000
      **  05 STMT-DETL-REC2            PIC  X(1109).                    01251023
          05 STMT-DETL-REC2            PIC  X(1909).                    01252023
                                                                        01260000
       FD DRBT-IO-FL                                                    01270000
           RECORD CONTAINS 950 CHARACTERS                               01280000
           DATA RECORD       IS DRBT-IO-REC.                            01290000
       01 DRBT-IO-REC.                                                  01300000
          05 DRBT-CNTL-REC.                                             01310000
             10 DRBT-KEY-CUST-REF        PIC X(12).                     01320000
             10 DRBT-KEY-PROD-CODE       PIC X(03).                     01330000
             10 DRBT-KEY-PROD-CODE1      PIC X(01).                     01331052
             10 DRBT-KEY-PROD-CODE2      PIC X(03).                     01332052
             10 DRBT-KEY-DRBT-ACCT       PIC X(10).                     01340000
             10 DRBT-KEY-COMP-ID         PIC X(12).                     01350000
      *>> R59060043                                                     01351000
             10 DRBT-KEY-CN-REF          PIC X(24).                     01352000
      *      10 DRBT-KEY-CN-REF          PIC X(13).                     01360000
      *<< R59060043                                                     01362000
             10 DRBT-KEY-CR-SEQ          PIC X(06).                     01370000
          05 DRBT-DATA-REC.                                             01380000
             10 DRBT-HEADER-REC.                                        01390000
                15 DRBT-HD-COMP-ID       PIC X(12).                     01400000
                15 DRBT-HD-TRANS-DATE    PIC X(21).                     01410000
                15 DRBT-HD-TRANS-AMT     PIC X(16).                     01420000
                15 DRBT-HD-ACTION-FLAG   PIC X(01).                     01430000
                15 DRBT-HD-DETAIL        PIC X(96).                     01440000
             10 DRBT-DR-REC.                                            01450000
                15 DRBT-DR-PROD-CODE     PIC X(03).                     01460000
                15 DRBT-DR-VA-DATE       PIC X(08).                     01470000
                15 DRBT-DR-DB-ACNO       PIC X(25).                     01480000
                15 DRBT-DR-AC-TYPE       PIC X(02).                     01490000
                15 DRBT-DR-DETAIL        PIC X(60).                     01500000
             10 DRBT-CR-REC.                                            01510000
                15 DRBT-CR-SEQ           PIC X(06).                     01520000
                15 DRBT-CR-ACCT          PIC X(25).                     01530000
                15 DRBT-CR-AMT           PIC X(16).                     01540000
                15 DRBT-CR-NET-AMT       PIC X(16).                     01550000
                15 DRBT-CR-FEE-BENE      PIC X(16).                     01560000
                15 DRBT-CR-STATUS        PIC X(01).                     01570000
                15 DRBT-CR-DETIAL-1      PIC X(497).                    01580000
                15 DRBT-CR-BENE-TAX-ID0.                                01580100
                   20 DRBT-CR-FEE-BNT-FLG   PIC X(01).                  01580200
                   20 DRBT-CR-BENE-TAX-ID   PIC X(09).                  01580300
      *>> CB64010022                                                    01582047
      *>>       15 DRBT-CR-DETIAL-2      PIC X(22).                     01582147
                15 DRBT-CR-DETIAL-2.                                    01582247
                   20 DRBT-CR-DETIAL-21     PIC X(08).                  01582347
                   20 DRBT-CR-FLG-EWT       PIC X(03).                  01582447
                   20 DRBT-CR-DETIAL-22     PIC X(11).                  01582547
      *>> R59060043                                                     01582647
          05 FILLER                      PIC X(25).                     01584052
      *>> 05 FILLER                      PIC X(29).                     01585052
      *   05 FILLER                      PIC X(40).                     01590000
      *<< R59060043                                                     01590100
          05 DRBT-BNT-PROCESS            PIC X(01).                     01591000
                                                                        01600000
       FD CRDT-IO-FL                                                    01610000
           RECORD CONTAINS 950 CHARACTERS                               01620000
           DATA RECORD       IS CRDT-IO-REC.                            01630000
       01 CRDT-IO-REC.                                                  01640000
          05 CRDT-CNTL-REC.                                             01650000
             10 CRDT-KEY-CUST-REF        PIC X(12).                     01660000
             10 CRDT-KEY-PROD-CODE       PIC X(03).                     01670000
             10 CRDT-KEY-PROD-CODE1      PIC X(01).                     01671054
             10 CRDT-KEY-PROD-CODE2      PIC X(03).                     01672054
             10 CRDT-KEY-DRBT-ACCT       PIC X(10).                     01680000
             10 CRDT-KEY-COMP-ID         PIC X(12).                     01690000
      *>> R59060043                                                     01692000
             10 CRDT-KEY-CN-REF          PIC X(24).                     01692100
      *      10 CRDT-KEY-CN-REF          PIC X(13).                     01692200
      *<< R59060043                                                     01693000
             10 CRDT-KEY-CR-SEQ          PIC X(06).                     01710000
          05 CRDT-DATA-REC.                                             01720000
             10 CRDT-HEADER-REC.                                        01730000
                15 CRDT-HD-COMP-ID       PIC X(12).                     01740000
                15 CRDT-HD-TRANS-DATE    PIC X(21).                     01750000
                15 CRDT-HD-TRANS-AMT     PIC X(16).                     01760000
                15 CRDT-HD-ACTION-FLAG   PIC X(01).                     01770000
                15 CRDT-HD-DETAIL        PIC X(96).                     01780000
             10 CRDT-DR-REC.                                            01790000
                15 CRDT-DR-PROD-CODE     PIC X(03).                     01800000
                15 CRDT-DR-VA-DATE       PIC X(08).                     01810000
                15 CRDT-DR-CD-ACNO       PIC X(25).                     01820000
                15 CRDT-DR-AC-TYPE       PIC X(02).                     01830000
                15 CRDT-DR-DETAIL        PIC X(60).                     01840000
             10 CRDT-CR-REC.                                            01850000
                15 CRDT-CR-SEQ           PIC X(06).                     01860000
                15 CRDT-CR-ACCT          PIC X(25).                     01870000
                15 CRDT-CR-AMT           PIC X(16).                     01880000
                15 CRDT-CR-NET-AMT       PIC X(16).                     01890000
                15 CRDT-CR-FEE-BENE      PIC X(16).                     01900000
                15 CRDT-CR-STATUS        PIC X(01).                     01921000
                15 CRDT-CR-DETIAL-1      PIC X(497).                    01922000
                15 CRDT-CR-BENE-TAX-ID0.                                01922100
                   20 CRDT-CR-FEE-BNT-FLG   PIC X(01).                  01922200
                   20 CRDT-CR-BENE-TAX-ID   PIC X(09).                  01922300
      *>> CB64010022                                                    01922447
      *>>       15 CRDT-CR-DETIAL-2      PIC X(22).                     01922547
                15 CRDT-CR-DETIAL-2.                                    01922647
                   20 CRDT-CR-DETIAL-21     PIC X(08).                  01922747
                   20 CRDT-CR-FLG-EWT       PIC X(03).                  01922847
                   20 CRDT-CR-DETIAL-22     PIC X(11).                  01922947
      *>> R59060043                                                     01925000
          05 FILLER                      PIC X(25).                     01931052
      *>> 05 FILLER                      PIC X(29).                     01931152
      *   05 FILLER                      PIC X(40).                     01931252
      *<< R59060043                                                     01931352
          05 CRDT-BNT-PROCESS            PIC X(01).                     01932000
                                                                        01940000
       FD  STMT-OUTD-FL                                                 01950000
           LABEL  RECORDS    ARE STANDARD                               01960000
     ***   RECORD CONTAINS   2400 CHARACTERS                            01970023
           RECORD CONTAINS   3200 CHARACTERS                            01971023
           DATA   RECORD     IS STMT-OUTD-REC.                          01980000
     **01  STMT-OUTD-REC               PIC X(2400).                     01990023
       01  STMT-OUTD-REC               PIC X(3200).                     01991023
                                                                        02000000
       FD  XCPT-OUT-FL                                                  02010000
           LABEL  RECORDS    ARE STANDARD                               02020000
           RECORD CONTAINS   279 CHARACTERS                             02030000
           DATA   RECORD     IS XCPT-OUT-REC.                           02040000
       01  XCPT-OUT-REC      PIC X(279).                                02050000
                                                                        02060000
       SD SORT-WORK                                                     02070000
            RECORDING MODE IS F.                                        02080000
       01 SORT-WORK-REC.                                                02090000
          05 SWK-CNTL-REC.                                              02100000
             10 SWK-IN-CORP-ID            PIC  X(16).                   02110000
             10 SWK-IN-ACCTNO             PIC  X(11).                   02120000
             10 SWK-IN-TSEQ-NO            PIC  9(06).                   ********
          05 SWK-DETL-REC.                                              ********
             10 SWK-STMT-REC.                                           ********
                15 SWK-REC-TYPE            PIC  X(01).                  ********
                15 FILLER                  PIC  X(05).                  ********
                15 SWK-STMT-ACCT-NO        PIC  X(10).                  ********
                15 SWK-STMT-BANK-ID        PIC  X(03).                  ********
                15 SWK-STMT-BRNO           PIC  X(04).                  ********
                15 SWK-STMT-TDATE.                                      ********
                   20 SWK-STMT-TDATE-YYYY  PIC  X(04).                  ********
                   20 SWK-STMT-TDATE-MM    PIC  X(02).                  ********
                   20 SWK-STMT-TDATE-DD    PIC  X(02).                  ********
                15 SWK-STMT-BAMT           PIC  X(16).                  ********
                15 SWK-STMT-BAMT-SIGN      PIC  X(01).                  ********
                15 SWK-STMT-TAMT           PIC  X(15).                  ********
                15 SWK-STMT-CRDR           PIC  X(02).                  ********
                15 SWK-STMT-TCODE          PIC  X(04).                  ********
                15 SWK-STMT-TERM           PIC  X(04).                  ********
                15 SWK-STMT-CHQNO          PIC  X(07).                  02310000
                15 SWK-STMT-REF-NUM        PIC  X(25).                  02320000
                15 FILLER                  PIC  X(02).                  02330000
                15 SWK-STMT-GROUP-ID       PIC  X(05).                  02340000
                15 SWK-STMT-SERIAL-NO      PIC  X(06).                  02350000
                15 SWK-STMT-CHQNO-10       PIC  X(10).                  02360000
                15 SWK-STMT-DESC           PIC  X(20).                  02370000
                15 SWK-STMT-DESC-0         PIC  X(20).                  02380000
                15 SWK-STMT-SYMBOL         PIC  X(03).                  02390000
                15 SWK-STMT-CHAN           PIC  X(04).                  02400000
             10 SWK-STMT-REC-ERP.                                       02410000
                15 SWK-STMT-TAMT-ERP       PIC  9(13)V99.               02420000
                15 SWK-STMT-CRDR-ERP       PIC  X(02).                  02430000
             10 SWK-STMT-DET-REC.                                       02440000
                15 SWK-COMP-ID            PIC  X(12).                   02450000
                15 SWK-TRANS-DATE         PIC  X(21).                   02460000
                15 SWK-TRANS-AMT          PIC  X(16).                   02470000
                15 SWK-ACTION-FLAG        PIC  X(01).                   02480000
                15 SWK-CN-REF             PIC  X(24).                   02490000
                15 SWK-S1-REF             PIC  X(32).                   02500000
                15 SWK-CUST-REF           PIC  X(40).                   02510000
                15 SWK-PROD-CODE          PIC  X(03).                   02520000
                15 SWK-VALUE-DATE         PIC  X(08).                   02530000
                15 SWK-DEBIT-ACCT         PIC  X(25).                   02540000
                15 SWK-DB-ACCT-TYPE       PIC  X(02).                   02550000
                15 SWK-DB-BRC-CODE        PIC  X(04).                   02560000
                15 SWK-DB-CURRENCY        PIC  X(03).                   02570000
                15 SWK-DB-AMT             PIC  X(16).                   02580000
                15 SWK-DB-FEE-ACCT        PIC  X(25).                   02590000
                15 SWK-DB-FEE-ACCT-TYPE   PIC  X(02).                   02600000
                15 SWK-DB-FEE-BRC-CODE    PIC  X(04).                   02610000
                15 SWK-NO-CREDIT          PIC  X(06).                   02620000
                15 SWK-CR-SEQ             PIC  X(06).                   02630000
                15 SWK-CR-ACCT            PIC  X(25).                   02640000
                15 SWK-CR-AMT             PIC  *************.99.        ********
                15 SWK-CR-NET-AMT         PIC  X(16).                   ********
                15 SWK-CR-BENE-FEE        PIC  X(16).                   ********
                15 SWK-TRANS-STATUS       PIC  X(01).                   ********
                15 SWK-TRANS-DES          PIC  X(100).                  ********
                15 SWK-CR-CURRENCY        PIC  X(03).                   ********
                15 SWK-RECEIVE-BANK-CD    PIC  X(03).                   ********
                15 SWK-RECEIVE-BANK-NAME  PIC  X(35).                   ********
                15 SWK-RECEIVE-BRC-CD     PIC  X(04).                   ********
                15 SWK-RECEIVE-BRC-NAME   PIC  X(35).                   ********
                15 SWK-WHT-PRESENT        PIC  X(01).                   ********
                15 SWK-INV-DET-PRESENT    PIC  X(01).                   ********
                15 SWK-CR-ADV-REQUIRE     PIC  X(01).                   ********
                15 SWK-DELIVERY-MODE      PIC  X(01).                   ********
                15 SWK-PICKUP-LOCATION    PIC  X(04).                   ********
                15 SWK-WHT-FORM-TYPE      PIC  X(02).                   ********
                15 SWK-WHT-TAX-NO         PIC  X(14).                   ********
                15 SWK-WHT-ATT-NO         PIC  X(06).                   ********
                15 SWK-NO-WHT-DET         PIC  X(02).                   ********
                15 SWK-TOT-WHT-AMT        PIC  X(16).                   02840000
                15 SWK-NO-INV-DET         PIC  X(06).                   02850000
                15 SWK-TOT-INV-AMT        PIC  X(16).                   02860000
                15 SWK-WHT-PAY-TYPE       PIC  X(01).                   02870000
                15 SWK-WHT-REMARK         PIC  X(40).                   02880000
                15 SWK-WHT-DEDUCT-CODE    PIC  X(08).                   02890000
                15 SWK-WHT-SIGNATORY      PIC  X(01).                   02900000
                15 SWK-BENE-NOTIFI        PIC  X(01).                   02910000
                15 SWK-CUST-REF-NO        PIC  X(20).                   02920000
                15 SWK-CHQ-REF-DOC-TYPE   PIC  X(01).                   02930000
                15 SWK-PAY-TYPE-CODE      PIC  X(03).                   02940000
                15 SWK-SERVICE-TYPE       PIC  X(02).                   02950000
                15 SWK-PAYEE-NAME-TH      PIC  X(100).                  02960000
                15 SWK-PAYEE-NAME-EN      PIC  X(70).                   02970000
                15 SWK-BENE-TAX-ID        PIC  X(10).                   02980000
                15 SWK-CHQ-NO             PIC  X(08).                   02990000
                15 SWK-WHT-SERIAL-NO      PIC  X(14).                   03000000
                15 SWK-FILLER             PIC  X(155).                  03010008
      **        15 SWK-FILLER             PIC  X(60).                   03010108
     ***     10 SWK-STMT-REC2             PIC  X(1100).                 03011023
     ***     10 SWK-STMT-REC2             PIC  X(1900).                 03012024
             10 SWK-STMT-REC2             PIC  X(1909).                 03013024
      *------------------------*                                        03020000
       WORKING-STORAGE SECTION.                                         03030000
      *------------------------*                                        03040000
       77 SW-OPENINFL                  PIC X(01)   VALUE 'Y'.           03050000
          88 ERR-OPENINFL                          VALUE 'N'.           03060000
          88 SUCCESS-OPENINFL                      VALUE 'Y'.           03070000
       77 SW-PRTFIRST                  PIC X(01)   VALUE 'Y'.           03080000
          88 PRINT-FIRST                           VALUE 'Y'.           03090000
          88 NOT-PRINT-FIRST                       VALUE 'N'.           03100000
       77 SW-ACCT-PYMT-FLG             PIC X(01)   VALUE 'N'.           03101002
          88 GEN-PYMT-STMTINF                      VALUE 'Y'.           03102002
          88 NOT-GEN-PYMT-STMTINF                  VALUE 'N'.           03103002
       77 SW-STMT-FIRST                PIC X(01)   VALUE 'Y'.           03110000
          88 STMT-FIRST                            VALUE 'Y'.           03120000
          88 NOT-STMT-FIRST                        VALUE 'N'.           03130000
       77 SW-DATEINFL                  PIC X(01)   VALUE 'N'.           03140000
          88 EOF-DATEINFL                          VALUE 'Y'.           03150000
          88 NOT-EOF-DATEINFL                      VALUE 'N'.           03160000
       77 SW-ACCTINF                   PIC X(01)   VALUE 'N'.           03170000
          88 EOF-ACCTINF                           VALUE 'Y'.           03180000
          88 NOT-EOF-ACCTINF                       VALUE 'N'.           03190000
       77 SW-STMTINF-EOF               PIC X(01)   VALUE 'N'.           03200000
          88 EOF-STMTINF                           VALUE 'Y'.           03210000
          88 NOT-EOF-STMTINF                       VALUE 'N'.           03220000
       77 SW-STMTDET-CHANGE            PIC X(01)   VALUE 'N'.           03230000
          88 CHANGE-STMTDET                        VALUE 'Y'.           03240000
          88 NOT-CHANGE-STMTDET                    VALUE 'N'.           03250000
       77 SW-STMTDET-EOF               PIC X(01)   VALUE 'N'.           03260000
          88 EOF-STMTDET                           VALUE 'Y'.           03270000
          88 NOT-EOF-STMTDET                       VALUE 'N'.           03280000
       77 SW-STMTDET-FOUND             PIC X(01)   VALUE 'Y'.           03290000
          88 FOUND-STMTDET                         VALUE 'Y'.           03300000
          88 NOT-FOUND-STMTDET                     VALUE 'N'.           03310000
       77 SW-STMTINF-FOUND             PIC X(01)   VALUE 'Y'.           03320000
          88 FOUND-STMTINF                         VALUE 'Y'.           03330000
          88 NOT-FOUND-STMTINF                     VALUE 'N'.           03340000
       77 SW-STMTINF-CHANGE            PIC X(01)   VALUE 'N'.           03350000
          88 CHANGE-STMTINF                        VALUE 'Y'.           03360000
          88 NOT-CHANGE-STMTINF                    VALUE 'N'.           03370000
       77 SW-BKCHK-EOF                 PIC X(01)   VALUE 'N'.           03380000
          88 EOF-BKCHKINF                          VALUE 'Y'.           03390000
          88 NOT-EOF-BKCHKINF                      VALUE 'N'.           03400000
       77 SW-BKCHKINF-CHANGE           PIC X(01)   VALUE 'N'.           03410000
          88 CHANGE-BKCHKINF                       VALUE 'Y'.           03420000
          88 NOT-CHANGE-BKCHKINF                   VALUE 'N'.           03430000
       77 SW-XCPTOUTF                  PIC X(01)   VALUE 'N'.           03440000
          88 FOUND-XCPTOUTF                        VALUE 'Y'.           03450000
          88 NOT-FOUND-XCPTOUTF                    VALUE 'N'.           03460000
       77 SW-DR-STMTDET-RTN            PIC X(01)   VALUE 'N'.           03461000
          88 Y-DR-STMTDET-RTN                      VALUE 'Y'.           03462000
          88 N-DR-STMTDET-RTN                      VALUE 'N'.           03463000
       77 SW-CR-STMTDET-RTN            PIC X(01)   VALUE 'N'.           03463100
          88 Y-CR-STMTDET-RTN                      VALUE 'Y'.           03463200
          88 N-CR-STMTDET-RTN                      VALUE 'N'.           03463300
       77 SW-BNT-DET-FND               PIC X(01)   VALUE 'N'.           03463400
          88 Y-BNT-DET-FND                         VALUE 'Y'.           03463500
          88 N-BNT-DET-FND                         VALUE 'N'.           03463600
       77 SW-GEN-DETAIL                PIC X(01)   VALUE 'N'.           03463700
          88 GEN-DETAIL                            VALUE 'Y'.           03463800
          88 NOT-GEN-DETAIL                        VALUE 'N'.           03463900
       77 SW-BNT-ROUTINE               PIC X(01)   VALUE 'N'.           03464038
          88 USE-BNT-ROUTINE                       VALUE 'Y'.           03466039
          88 NO-USE-BNT-ROUTINE                    VALUE 'N'.           03467039
       77 SW-EWT-TRANS                 PIC X(01)   VALUE 'N'.           03468059
          88 EWT-TRANS                             VALUE 'Y'.           03469059
          88 NOT-EWT-TRANS                         VALUE 'N'.           03469159
       77 SW-EWT-BNT-RTN               PIC X(01)   VALUE 'N'.           03469259
          88 USE-EWT-BNT-RTN                       VALUE 'Y'.           03469359
          88 NO-USE-EWT-BNT-RTN                    VALUE 'N'.           03469459
      *------------------------------------------------------*          03470000
       01 WK-FILE-STATUS.                                               03490000
          05 ACCT-IN-STAT           PIC  X(02).                         03500000
          05 STMT-IN-STAT           PIC  X(02).                         03510000
          05 DRBT-IO-STAT           PIC  X(02).                         03520000
          05 CRDT-IO-STAT           PIC  X(02).                         03530000
          05 BRAN-IN-STAT           PIC  X(02).                         03540000
          05 STMT-OUTD-STAT         PIC  X(02).                         03550000
          05 STMT-OUTM-STAT         PIC  X(02).                         03560000
          05 XCPT-OUT-STAT          PIC  X(02).                         03570000
                                                                        ********
       01 WK-TMP-VALUE.                                                 ********
          05 WK-CURR-PROD-CODE     PIC X(03).                           ********
          05 WK-CURR-TRANS-STATUS  PIC X(01).                           ********
          05 WK-CURR-TRANS-AMT     PIC 9(14)V99.                        ********
          05 WK-CN-TRANS-AMT       PIC 9(14)V99.                        ********
          05 WK-FEE-RECV-BANK      PIC 9(7)V99.                         ********
          05 WK-NEW-BAL-AMT        PIC S9(14)V99.                       ********
                                                                        ********
       01 CURR-DATE.                                                    ********
          03 CURR-DATE-YYYY.                                            ********
             10 CURR-DATE-CC       PIC 9(02)   VALUE 20.                ********
             10 CURR-DATE-YY       PIC 9(02).                           ********
          03 FILLER                PIC X(01)   VALUE '/'.               ********
          03 CURR-DATE-MM          PIC 9(02).                           ********
          03 FILLER                PIC X(01)   VALUE '/'.               ********
          03 CURR-DATE-DD          PIC 9(02).                           ********
                                                                        ********
       01 TODAY-DATE.                                                   03740000
          03 TODAY-DATE-YYYY       PIC 9999.                            03750000
          03 TODAY-DATE-MM         PIC 99.                              03760000
          03 TODAY-DATE-DD         PIC 99.                              03770000
                                                                        03780000
       01 TODAY-TIME.                                                   03790000
          03 TODAY-TIME-HH         PIC 99.                              03800000
          03 TODAY-TIME-MM         PIC 99.                              03810000
          03 TODAY-TIME-SS         PIC 99.                              03820000
                                                                        03830000
       01 WK-ACCT-11D.                                                  03840000
          05 WK-BB-11               PIC  9(03).                         03850000
          05 WK-T0-11               PIC  9(01).                         03860000
          05 WK-TT-11               PIC  9(01).                         03870000
          05 WK-SS-11               PIC  9(05).                         03880000
          05 WK-CHKD-11             PIC  9(01).                         03890000
                                                                        03900000
       01 WK-ACCT-10D.                                                  03910000
          05 WK-BB-10               PIC  9(03).                         03920000
          05 WK-TT-10               PIC  9(01).                         03930000
          05 WK-SS-10               PIC  9(05).                         03940000
          05 WK-CHKD-10             PIC  9(01).                         03950000
                                                                        03960000
      *>> R59060091                                                     03961000
       01 WS-STMT-TOTAL-DR       PIC  9(14)V99.                         03961100
       01 WS-STMT-TOTAL-CR       PIC  9(14)V99.                         03961200
       01 WS-CHK-ACCT.                                                  03961600
          03  WS-INP-ACCT-NO           PIC X(10) VALUE SPACE.           03961700
          03  FILLER        REDEFINES  WS-INP-ACCT-NO.                  03961800
              05  WS-INP-ACCT-BRN      PIC X(03).                       03961900
              05  WS-INP-ACCT-TYP      PIC X(01).                       03962000
              05  WS-INP-ACCT-SEQ      PIC X(06).                       03962100
          03  WS-ACCT-BRN              PIC X(03).                       03962200
              88  WS-B1K-ACCT          VALUE '401' THRU '469'.          03962300
              88  WS-B1K-SV-ACCT       VALUE '401' THRU '460'.          03962400
              88  WS-B1K-FX-ACCT       VALUE '461' THRU '465'.          03962500
              88  WS-B1K-LT-ACCT       VALUE '466' THRU '467'.          03962600
              88  WS-B1K-CA-ACCT       VALUE '468' THRU '469'.          03962700
          03  WS-ACCT-TYPE             PIC X.                           ********
              88  WS-FX-ACCT           VALUE  '1'.                      ********
              88  WS-SV-ACCT           VALUE  '2' '4' '5' '6'.          ********
              88  WS-LT-ACCT           VALUE  '0' '8'.                  ********
              88  WS-CA-ACCT           VALUE  '3'.                      ********
          03  WS-SV-ACCT-DESC          PIC X(35) VALUE                  ********
              'Saving Account                     '.                    ********
          03  WS-FX-ACCT-DESC          PIC X(35) VALUE                  ********
              'Fixed Account                      '.                    ********
          03  WS-LT-ACCT-DESC          PIC X(35) VALUE                  ********
              'Long Term Account                  '.                    ********
          03  WS-CA-ACCT-DESC          PIC X(35) VALUE                  ********
              'Current Account                    '.                    ********
      *<< R59060091                                                     ********
                                                                        ********
       01 WK-CNV-NUMERIC.                                               ********
          05 WK-STMT-IN-TAMT        PIC  9(13)V99.                      ********
          05 WK-STMT-NEW-TAMT       PIC  9(13)V99.                      ********
          05 WK-DIFF-TAMT           PIC  9(13)V99.                      ********
          05 WK-CNV-DRTOT           PIC  9(13)V99.                      ********
          05 WK-CNV-CRTOT           PIC  9(13)V99.                      ********
          05 WK-BC-BCHQAMT          PIC  9(13)V99.                      ********
          05 WK-STMT-IN-BAMT        PIC S9(14)V99.                      ********
          05 WK-STMT-NEW-BAMT       PIC S9(14)V99.                      ********
          05 WK-PREV-IN-BAMT        PIC S9(14)V99.                      04060000
          05 WK-STR-STRBAL          PIC S9(14)V99.                      04070000
          05 WK-TMP-H-OBAL          PIC 9(16) VALUE ZEROES.             04080000
          05 WK-TMP-H-OBAL-9 REDEFINES WK-TMP-H-OBAL PIC 9(14)V99.      04090000
                                                                        04100000
       01 WK-CNV-DISPLAY.                                               04110000
          05 WK-CNV-NEW-BAMT        PIC  *************9.99.             04120000
          05 WK-CNV-STRBAL-AMT      PIC +*************999.              04130000
          05 WK-CNV-STRBAL          PIC -*************9.99.             04140000
          05 WK-CNV-ENDBAL          PIC -*************9.99.             04150000
          05 WK-CNV-TCOUNT          PIC  999999.                        04160000
          05 WK-CNV-BCHQAMT         PIC  *************.99.              04170000
                                                                        04180000
       01 WK-PAGE-NO                PIC 9(03)    VALUE ZEROS.           04190000
       01 CENTER-BR                 PIC 9(04)    VALUE 0474.            04200000
       01 WK-LINE-NO                PIC 9(02)    VALUE ZEROS.           04210000
       01 WK-TRAN-SEQ               PIC 9(09)    VALUE ZEROS.           04220000
       01 TOT-CRDT                 PIC 9(09)     VALUE ZEROS.           04230000
       01 CURRENT-DATE              PIC X(08)    VALUE SPACES.          04240000
       01 TIME-OF-DAY               PIC 9(06)    VALUE ZEROS.           04250000
       01 I                         PIC 9(05)    VALUE ZEROS.           04260000
       01 J                         PIC 9(05)    VALUE ZEROS.           04270000
                                                                        04280000
       01 WK-REPORT-KEY.                                                04290000
          05 WK-CHANNEL-KEY         PIC X(04).                          ********
                                                                        ********
       01 WK-HEAD-IN.                                                   ********
          05 WK-H-RECTYPE           PIC  X(01).                         ********
          05 FILLER                 PIC  X(05).                         ********
          05 WK-H-ACCNUM            PIC  X(10).                         ********
          05 WK-H-BANK-ID           PIC  X(03).                         ********
          05 WK-H-BRNO              PIC  X(04).                         ********
          05 WK-H-OPEN-DATE.                                            ********
             10 WK-H-OPNDATE-DD     PIC  X(02).                         ********
             10 WK-H-OPNDATE-MM     PIC  X(02).                         ********
             10 WK-H-OPNDATE-YYYY   PIC  X(04).                         ********
          05 WK-H-OPEN-BAL          PIC  X(16).                         ********
          05 WK-H-OPEN-BAL-TYP      PIC  X(01).                         ********
          05 WK-H-ACCNAM            PIC  X(30).                         ********
          05 FILLER                 PIC  X(29).                         ********
          05 WK-H-GROUP-ID          PIC  X(05).                         ********
          05 WK-H-SERIAL-NO         PIC  X(06).                         ********
      *>> R59060091                                                     04470100
          05 FILLER                 PIC  X(10).                         04471000
          05 WK-H-DR-TOTAL          PIC  9(14)V99.                      04472000
          05 WK-H-DR-TOTAL-TYP      PIC  X(01).                         04473000
          05 WK-H-CR-TOTAL          PIC  9(14)V99.                      04474000
          05 WK-H-CR-TOTAL-TYP      PIC  X(01).                         04475000
          05 WK-H-CLS-BAL           PIC  X(16).                         04476000
          05 WK-H-CLS-BAL-TYP       PIC  X(01).                         04477000
          05 WK-H-ACC-TYPE          PIC  X(35).                         04478000
          05 WK-H-CCY               PIC  X(03).                         04479000
          05 WK-H-TOTAL-DITEM       PIC  X(06).                         04479100
          05 FILLER                 PIC  X(977) VALUE SPACES.           04480000
          05 FILLER                 PIC  X(1200) VALUE SPACES.          04480107
      *   05 FILLER                 PIC  X(1082) VALUE SPACES.          04480207
      *<< R59060091                                                     ********
                                                                        ********
       01 WK-FOOT-IN.                                                   ********
          05 WK-T-RECTYPE           PIC  X(01).                         ********
          05 FILLER                 PIC  X(05).                         ********
          05 WK-T-ACCNUM            PIC  X(10).                         ********
          05 WK-T-BANK-ID           PIC  X(03).                         ********
          05 WK-T-BRNO              PIC  X(04).                         ********
          05 WK-T-CLOSE-DATE.                                           ********
             10 WK-T-CLSDATE-DD     PIC  X(02).                         ********
             10 WK-T-CLSDATE-MM     PIC  X(02).                         ********
             10 WK-T-CLSDATE-YYYY   PIC  X(04).                         ********
          05 WK-T-CLS-BAL           PIC  X(16).                         ********
          05 WK-T-CLS-BAL-TYP       PIC  X(01).                         ********
          05 WK-T-AVL-BAL           PIC  X(16).                         ********
          05 WK-T-AVL-BAL-TYP       PIC  X(01).                         ********
          05 WK-T-TOTAL-ITEM        PIC  X(06).                         ********
          05 FILLER                 PIC  X(36).                         ********
          05 WK-T-GROUP-ID          PIC  X(05).                         04660000
          05 WK-T-SERIAL-NO         PIC  X(06).                         04670000
          05 FILLER                 PIC  X(1082) VALUE SPACES.          04680000
          05 FILLER                 PIC  X(1200) VALUE SPACES.          04681007
                                                                        04690000
       01 XCPT-PRT-TAB.                                                 04700000
          05 XCPT-P01-TRAN-SEQ           PIC  X(09).                    04710000
          05 XCPT-P01-PROD-CODE          PIC  X(04).                    04720000
          05 XCPT-P01-TDATE-YYYY         PIC  X(04).                    04730000
          05 XCPT-P01-TDATE-MM           PIC  X(02).                    04740000
          05 XCPT-P01-TDATE-DD           PIC  X(02).                    04750000
          05 XCPT-P01-VDATE-YYYY         PIC  X(04).                    04760000
          05 XCPT-P01-VDATE-MM           PIC  X(02).                    04770000
          05 XCPT-P01-VDATE-DD           PIC  X(02).                    04780000
          05 XCPT-P01-STM-CODE           PIC  X(03).                    04790000
          05 XCPT-P01-CHN-CODE           PIC  X(04).                    04800000
          05 XCPT-P01-TRAN-TYPE          PIC  X(01).                    04810000
          05 XCPT-P01-TRAN-AMT           PIC  9(13)V99.                 04820000
          05 XCPT-P01-LINK-KEY           PIC  X(20).                    04830000
          05 XCPT-P01-S1-BAT-REF         PIC  X(20).                    ********
          05 XCPT-P02-TAB         OCCURS 1 TO 20000                     ********
                   DEPENDING ON TOT-CRDT INDEXED BY CRDT-IDX.           ********
             10 XCPT-P02-SEQ-NO          PIC 9(09).                     ********
             10 XCPT-P02-BEN-NAME        PIC X(70).                     ********
             10 XCPT-P02-BEN-ACCT        PIC X(25).                     ********
             10 XCPT-P02-BEN-BANK-CODE   PIC X(03).                     ********
             10 XCPT-P02-NET-AMT         PIC 9(13)V99.                  ********
             10 XCPT-P02-CHQ-NO          PIC X(08).                     ********
             10 XCPT-P02-STATUS          PIC X(01).                     ********
             10 XCPT-P02-ERROR-CODE      PIC X(20).                     ********
             10 XCPT-P02-CUST-REF        PIC X(20).                     ********
                                                                        ********
       01 REPORT-NO-TABLE.                                              ********
          05 FILLER           PIC X(47) VALUE                           ********
             'ERP RERPX002 Exception Report 2400 Statement'.            ********
                                                                        ********
       01 FILLER REDEFINES REPORT-NO-TABLE.                             ********
          05 REPORT-NO-TAB    OCCURS 1 TIMES INDEXED BY REPORT-INDEX.   05020000
             10 REPORT-KEY.                                             05030000
                15 REPORT-CHANNEL        PIC X(4).                      05040000
             10 REPORT-NO                PIC X(8).                      05050000
             10 REPORT-NAME              PIC X(35).                     05060000
                                                                        05070000
       01  HEAD-01.                                                     05080000
           03  FILLER        PIC X(48) VALUE SPACES.                    05090000
           03  FILLER        PIC X(17) VALUE 'S I A M   C O M M'.       05100000
           03  FILLER        PIC X(22) VALUE ' E R C I A L   B A N K'.  05110000
           03  FILLER        PIC X(45) VALUE SPACES.                    05120000
                                                                        05130000
       01  HEAD-02-1.                                                   05140000
           03  FILLER             PIC X(12)  VALUE ' Report No. '.      05150000
           03  H02-REPORT-NO      PIC X(8).                             05160000
           03  FILLER             PIC X(31)  VALUE  SPACES.             ********
           03  H02-REPORT-NAME    PIC X(35).                            ********
           03  FILLER             PIC X(36)  VALUE  SPACES.             ********
                                                                        ********
       01  HEAD-02.                                                     ********
           03  FILLER             PIC X(14)  VALUE ' Acct. No.  : '.    ********
           03  H03-ACCOUNT        PIC X(10).                            ********
           03  FILLER             PIC X(16)  VALUE  SPACES.             ********
           03  H03-ACCT-NAME      PIC X(13)  VALUE 'Acct. Name : '.     ********
           03  H03-ACCOUNT-NAME   PIC X(70).                            ********
           03  FILLER             PIC X(146) VALUE  SPACES.             ********
           03  FILLER             PIC X(07)  VALUE 'Page.'.             ********
           03  H02-PAGE           PIC ZZ9.                              ********
                                                                        ********
       01  HEAD-03.                                                     ********
           03  FILLER             PIC X(14)  VALUE ' S1 Corp. ID: '.    ********
           03  H03-S1-CORP-ID     PIC X(16).                            ********
           03  FILLER             PIC X(10)  VALUE SPACES.              ********
           03  H03-RDATE          PIC X(13)  VALUE 'Run Date   : '.     ********
           03  H03-RDATE-DD       PIC X(02).                            ********
           03  FILLER             PIC X(01)  VALUE '/'.                 ********
           03  H03-RDATE-MM       PIC X(02).                            05380000
           03  FILLER             PIC X(01)  VALUE '/'.                 05390000
           03  H03-RDATE-YYYY     PIC X(04).                            05400000
           03  FILLER             PIC X(216)  VALUE  SPACES.            05410000
                                                                        05420000
       01  HEAD-04.                                                     05430000
           03  FILLER             PIC X(10)  VALUE  ' Tran Seq.'.       05440000
           03  FILLER             PIC X(13)  VALUE  SPACES.             05450000
           03  FILLER             PIC X(08)  VALUE  'Prd Code'.         05460000
           03  FILLER             PIC X(59)  VALUE  SPACES.             05470000
           03  FILLER             PIC X(10)  VALUE  'Tran Date '.       05480000
           03  FILLER             PIC X(18)  VALUE  SPACES.             05490000
           03  FILLER             PIC X(10)  VALUE  '          '.       05500000
           03  FILLER             PIC X(21)  VALUE  SPACES.             05510000
           03  FILLER             PIC X(09)  VALUE  'Stm. Code'.        05520000
           03  FILLER             PIC X(12)  VALUE  SPACES.             05530000
           03  FILLER             PIC X(09)  VALUE  'Chn. Code'.        05540000
           03  FILLER             PIC X(06)  VALUE  SPACES.             05550000
           03  FILLER             PIC X(03)  VALUE  'D/C'.              05560000
           03  FILLER             PIC X(11)  VALUE  SPACES.             05570000
           03  FILLER             PIC X(11)  VALUE  'Tran Amount'.      05580000
           03  FILLER             PIC X(18)  VALUE  SPACES.             05590000
           03  FILLER             PIC X(08)  VALUE  'Stm Des.'.         05600000
           03  FILLER             PIC X(17)  VALUE  SPACES.             05610000
           03  FILLER             PIC X(12)  VALUE  '            '.     05620000
           03  FILLER             PIC X(15)  VALUE  SPACES.             ********
                                                                        ********
       01  HEAD-05.                                                     ********
           03  FILLER             PIC X(12)  VALUE  SPACE.              ********
           03  FILLER             PIC X(07)  VALUE  'Seq. No'.          ********
           03  FILLER             PIC X(15)  VALUE  SPACE.              ********
           03  FILLER             PIC X(13)  VALUE  'Bank Code    '.    ********
           03  FILLER             PIC X(10)  VALUE  SPACE.              ********
           03  FILLER             PIC X(10)  VALUE  'Net Amount'.       ********
           03  FILLER             PIC X(10)  VALUE  SPACE.              ********
           03  FILLER             PIC X(06)  VALUE  'Status'.           ********
           03  FILLER             PIC X(20)  VALUE  SPACES.             ********
           03  FILLER             PIC X(10)  VALUE  'Error Code'.       ********
           03  FILLER             PIC X(36)  VALUE  SPACES.             ********
           03  FILLER             PIC X(09)  VALUE  '         '.        ********
           03  FILLER             PIC X(12)  VALUE  SPACES.             ********
           03  FILLER             PIC X(08)  VALUE  '        '.         ********
           03  FILLER             PIC X(23)  VALUE  SPACE.              ********
           03  FILLER             PIC X(08)  VALUE  '        '.         ********
           03  FILLER             PIC X(62)  VALUE  SPACE.              05845000
           03  FILLER             PIC X(07)  VALUE  '       '.          05846000
           03  FILLER             PIC X(02)  VALUE  SPACE.              05847000
                                                                        05850000
       01  HEAD-LINE.                                                   05860000
           03  FILLER             PIC X(280)  VALUE ALL '-'.            05870000
                                                                        05880000
       01 SPACE-LINE.                                                   05890000
          03 FILLER                  PIC X(100) VALUE SPACES.           05900000
          03 SPACE-NO-DATA           PIC X(80) VALUE SPACES.            05910000
          03 FILLER                  PIC X(100) VALUE SPACES.           05920000
                                                                        05930000
       01 EOF-DATA.                                                     05940000
          03 FILLER             PIC X(20)  VALUE ' *** End of Data ***'.05950000
          03 FILLER             PIC X(260) VALUE SPACES.                05960000
                                                                        05970000
       01  PRT-01.                                                      05980000
           03  FILLER             PIC X(01)  VALUE   SPACES.            05990000
           03  P01-TRAN-SEQ       PIC X(09).                            06000000
           03  FILLER             PIC X(13)  VALUE  SPACES.             06010000
           03  P01-PROD-CODE      PIC X(04).                            06020000
           03  FILLER             PIC X(63)  VALUE  SPACES.             06030000
           03  P01-TDATE-DD       PIC X(02).                            06040000
           03  FILLER             PIC X(01)  VALUE '/'.                 06050000
           03  P01-TDATE-MM       PIC X(02).                            06060000
           03  FILLER             PIC X(01)  VALUE '/'.                 06070000
           03  P01-TDATE-YYYY     PIC X(04).                            06080000
           03  FILLER             PIC X(18)  VALUE SPACES.              06090000
           03  P01-VDATE-DD       PIC X(02)  VALUE SPACES.              06100000
           03  FILLER             PIC X(01)  VALUE ' '.                 06110000
           03  P01-VDATE-MM       PIC X(02)  VALUE SPACES.              06120000
           03  FILLER             PIC X(01)  VALUE ' '.                 06130000
           03  P01-VDATE-YYYY     PIC X(04)  VALUE SPACES.              06140000
           03  FILLER             PIC X(21)  VALUE  SPACE.              06150000
           03  P01-STM-CODE       PIC X(03).                            06160000
           03  FILLER             PIC X(18)  VALUE  SPACE.              06170000
           03  P01-CHN-CODE       PIC X(04).                            06180000
           03  FILLER             PIC X(11)  VALUE  SPACE.              06190000
           03  P01-TRAN-TYPE      PIC X(01).                            06200000
           03  FILLER             PIC X(04)  VALUE  SPACE.              06210000
           03  P01-TRAN-AMT       PIC Z,ZZZ,ZZZ,ZZZ,ZZ9.99.             06220000
           03  FILLER             PIC X(17)  VALUE  SPACE.              06230000
           03  P01-LINK-KEY       PIC X(20).                            06240000
           03  FILLER             PIC X(06)  VALUE  SPACE.              06250000
           03  P01-S1-BAT-REF     PIC X(20).                            06260000
           03  FILLER             PIC X(31)  VALUE  SPACE.              ********
                                                                        ********
       01  PRT-02.                                                      ********
           03  FILLER             PIC X(12)  VALUE  SPACES.             ********
           03  P02-SEQ-NO         PIC 9(06).                            ********
           03  FILLER             PIC X(16)  VALUE  SPACE.              ********
           03  P02-BEN-BANK-CODE  PIC X(03).                            ********
           03  FILLER             PIC X(10)  VALUE  SPACE.              ********
           03  P02-NET-AMT        PIC Z,ZZZ,ZZZ,ZZZ,ZZ9.99.             ********
           03  FILLER             PIC X(10)  VALUE  SPACE.              ********
           03  P02-STATUS         PIC X(01).                            ********
           03  FILLER             PIC X(25)  VALUE  SPACE.              ********
           03  P02-ERROR-CODE     PIC X(40).                            ********
           03  FILLER             PIC X(06)  VALUE  SPACE.              ********
           03  P02-CUST-REF       PIC X(20).                            ********
           03  FILLER             PIC X(03)  VALUE  SPACE.              ********
           03  P02-BEN-ACCT       PIC X(25).                            ********
           03  FILLER             PIC X(13)  VALUE  SPACE.              ********
           03  P02-BEN-NAME       PIC X(60).                            06472000
           03  FILLER             PIC X(02)  VALUE  SPACE.              06472100
           03  P02-CHQ-NO         PIC X(08).                            06473000
      *    03  FILLER             PIC X(11)  VALUE  SPACE.              06480000
                                                                        06490000
      *-----RMPSUB AREA-------*                                         06500000
       01  W-SEND                       PIC X(158).                     06510000
       01  FILLER          REDEFINES    W-SEND.                         06520000
           05 SERVICE-REQUEST-CODE      PIC 9(01).                      06530000
           05 W-RECORD                  PIC X(157).                     06540000
           05 INT-RECORD   REDEFINES    W-RECORD.                       06550000
              10  INT-TOTAL-REPORT      PIC 9(01).                      06560000
              10  INT-REPORT-NUMBER     PIC X(72).                      06570000
              10  FILLER   REDEFINES    INT-REPORT-NUMBER.              06580000
                  15  INT-REPORT-NO     PIC X(08)  OCCURS  9  TIMES.    06590000
              10  FILLER                PIC X(84).                      06600000
           05 TRF-RECORD   REDEFINES    W-RECORD.                       06610000
              10  TRF-REPORT-NUMBER     PIC X(08).                      06620000
              10  TRF-REPORT-TO-NUMBER  PIC 9(04)  COMP.                06630000
              10  TRF-RUNNING-NUMBER    PIC 9(02).                      06640000
              10  TRF-CHANNEL-CONTROL   PIC 9(02).                      06650000
              10  TRF-LINE-COUNTER      PIC 9(07)  COMP-3.              06660000
              10  FILLER                PIC X(07).                      06670000
              10  TRF-REPORT-DATA       PIC X(132).                     06680000
           05 TER-RECORD   REDEFINES    W-RECORD.                       06690000
              10  FILLER                PIC X(157).                     06700000
      *------------------------------------------------------*          06710000
       PROCEDURE DIVISION.                                              06720000
      *-------------------*                                             06730000
       000-MAIN-PROCESS.                                                06740000
                                                                        06750000
           PERFORM  1000-OPENFL-RTN       THRU 1000-EXIT.               06760000
           PERFORM  2000-MAIN-PROCESS-RTN THRU 2000-EXIT.               06770000
           PERFORM  9999-CLOSEFL-RTN      THRU 9999-EXIT.               06780000
           STOP   RUN.                                                  06790000
                                                                        06800000
       000-EXIT.    EXIT.                                               06810000
      *-------------------------------------------------------*         06820000
       1000-OPENFL-RTN.                                                 06830000
                                                                        06840000
           SET SUCCESS-OPENINFL TO TRUE.                                06850000
                                                                        06860000
           OPEN INPUT ACCT-IN-FL.                                       06870000
           IF ACCT-IN-STAT NOT = '00'                                   06880000
              DISPLAY '*** PROGRAM STMMCG05 ***'  UPON CONSOLE          06890045
              DISPLAY 'OPEN ACCTINF IN ERROR,CODE = ' ACCT-IN-STAT      06900000
                                                  UPON CONSOLE          06910000
              DISPLAY 'OPEN ACCTINF IN ERROR,CODE = ' ACCT-IN-STAT      06920000
              SET ERR-OPENINFL TO TRUE                                  06930000
           END-IF.                                                      06940000
                                                                        06950000
           OPEN INPUT STMT-IN-FL.                                       06960000
           IF STMT-IN-STAT NOT = '00' AND                               06970000
              STMT-IN-STAT NOT = '35'                                   06980000
              DISPLAY '*** PROGRAM STMMCG05 ***'  UPON CONSOLE          06990045
              DISPLAY 'OPEN STMTINF IN ERROR,CODE = ' STMT-IN-STAT      07000000
                                                  UPON CONSOLE          07010000
              DISPLAY 'OPEN STMTINF IN ERROR,CODE = ' STMT-IN-STAT      07020000
              SET ERR-OPENINFL TO TRUE                                  07030000
           END-IF.                                                      07040000
                                                                        07050000
           OPEN I-O   DRBT-IO-FL.                                       07060000
           IF DRBT-IO-STAT NOT = '00' AND                               07070000
              DRBT-IO-STAT NOT = '35'                                   07080000
              DISPLAY '*** PROGRAM STMMCG05 ***'  UPON CONSOLE          07090045
              DISPLAY 'OPEN DRBTIOF ERROR,CODE = ' DRBT-IO-STAT         07100000
                                                  UPON CONSOLE          07110000
              DISPLAY 'OPEN DRBTIOF ERROR,CODE = ' DRBT-IO-STAT         07120000
              SET ERR-OPENINFL TO TRUE                                  07130000
           END-IF.                                                      07140000
                                                                        07150000
           OPEN I-O   CRDT-IO-FL.                                       07160000
           IF CRDT-IO-STAT NOT = '00' AND                               07170000
              CRDT-IO-STAT NOT = '35'                                   07180000
              DISPLAY '*** PROGRAM STMMCG05 ***'  UPON CONSOLE          07190045
              DISPLAY 'OPEN CRDTIOF ERROR,CODE  := ' CRDT-IO-STAT       07200000
                                                  UPON CONSOLE          07210000
              DISPLAY 'OPEN CRDTIOF ERROR,CODE  := ' CRDT-IO-STAT       07220000
              SET ERR-OPENINFL TO TRUE                                  07230000
           END-IF.                                                      07240000
                                                                        07250000
           OPEN OUTPUT  STMT-OUTD-FL.                                   07260000
           IF STMT-OUTD-STAT NOT = '00'                                 07270000
              DISPLAY '*** PROGRAM STMMCG05 ***'      UPON CONSOLE      07280045
              DISPLAY 'OPEN STMTOUTD ERROR,CODE  = ' STMT-OUTD-STAT     07290000
                                                      UPON CONSOLE      07300000
              DISPLAY 'OPEN STMTOUTD ERROR,CODE  = ' STMT-OUTD-STAT     07310000
              SET ERR-OPENINFL TO TRUE                                  07320000
           END-IF.                                                      07330000
                                                                        07340000
           OPEN OUTPUT  XCPT-OUT-FL.                                    07350000
           IF XCPT-OUT-STAT NOT = '00'                                  07360000
              DISPLAY '*** PROGRAM STMMCG05 ***'      UPON CONSOLE      07370045
              DISPLAY 'OPEN XCPTOUTF ERROR,CODE  = ' XCPT-OUT-STAT      07380000
                                                      UPON CONSOLE      07390000
              DISPLAY 'OPEN XCPTOUTF ERROR,CODE  = ' XCPT-OUT-STAT      07400000
              SET ERR-OPENINFL TO TRUE                                  07410000
           END-IF.                                                      07420000
       1000-EXIT. EXIT.                                                 07430000
      *-------------------------------------------------------*         07440000
       2000-MAIN-PROCESS-RTN.                                           07450000
           IF ERR-OPENINFL                                              07460000
              GO TO 2000-EXIT                                           07470000
           END-IF.                                                      07480000
           SET NOT-EOF-ACCTINF     TO TRUE.                             07490000
           SET NOT-FOUND-XCPTOUTF  TO TRUE.                             07500000
           SET PRINT-FIRST         TO TRUE.                             07510000
           MOVE FUNCTION CURRENT-DATE(1:8) TO CURRENT-DATE.             07520000
           MOVE CURRENT-DATE       TO TODAY-DATE.                       07530000
           ACCEPT TODAY-TIME     FROM TIME.                             07540000
           PERFORM 2020-CLEAR-OCCUR-XCPT  THRU 2020-EXIT.               07550000
           PERFORM 5000-RMP-INIT-RTN THRU 5000-RMP-INIT-EXIT.           07560000
           IF STMT-IN-STAT  = '35'                                      07570000
              PERFORM 5100-PRT-HEADER        THRU 5100-EXIT             07580000
              PERFORM 5500-PRT-NO-EXCEPTION  THRU 5500-EXIT             07590000
              GO TO 2000-EXIT                                           07600000
           END-IF.                                                      07610000
           PERFORM 2100-READ-ACCTINF-RTN THRU 2100-EXIT.                07620000
           PERFORM UNTIL EOF-ACCTINF                                    07630000
              SET FOUND-STMTDET       TO TRUE                           07640000
              SET PRINT-FIRST         TO TRUE                           07650000
              MOVE FUNCTION CURRENT-DATE(1:8) TO CURRENT-DATE           07660000
              MOVE CURRENT-DATE       TO TODAY-DATE                     07670000
              ACCEPT TODAY-TIME     FROM TIME                           07680000
              PERFORM 2020-CLEAR-OCCUR-XCPT  THRU 2020-EXIT             07690000
              SORT  SORT-WORK ON ASCENDING KEY                          07700000
                                 SWK-IN-CORP-ID                         07710000
                                 SWK-IN-ACCTNO                          07720000
                                 SWK-IN-TSEQ-NO                         07730000
              INPUT  PROCEDURE                                          07740000
                       3000-SORT-INPUT-RTN  THRU 3000-EXIT              07750000
              OUTPUT PROCEDURE                                          07760000
                       4000-SORT-OUTPUT-RTN THRU 4000-EXIT              07770000
              IF NOT-FOUND-STMTDET                                      07780000
                 PERFORM 5400-PRT-EOF-DATA      THRU 5400-EXIT          07790000
              END-IF                                                    07800000
              PERFORM 2100-READ-ACCTINF-RTN THRU 2100-EXIT              07810000
           END-PERFORM.                                                 07820000
           IF NOT-FOUND-XCPTOUTF                                        07830000
              PERFORM 5101-PRT-HEADER        THRU 5101-EXIT             07840000
              PERFORM 5500-PRT-NO-EXCEPTION  THRU 5500-EXIT             07850000
           END-IF.                                                      07880000
                                                                        07890000
       2000-EXIT. EXIT.                                                 07900000
      *-------------------------------------------------------*         07910000
       2100-READ-ACCTINF-RTN.                                           07920000
                                                                        07930000
           SET NOT-GEN-PYMT-STMTINF  TO TRUE.                           07931002
                                                                        07932002
           READ ACCT-IN-FL AT END                                       07940000
                SET EOF-ACCTINF TO TRUE.                                07950000
                                                                        07950102
           IF ACCT-PAYMENT-FLG = 'Y'                                    07951002
              SET GEN-PYMT-STMTINF  TO TRUE.                            07952002
                                                                        07960000
       2100-EXIT. EXIT.                                                 07970000
      *-------------------------------------------------------*         07980000
       2020-CLEAR-OCCUR-XCPT.                                           07990000
                                                                        08000000
           MOVE ZEROS                 TO I.                             08010000
           MOVE ZEROS                 TO TOT-CRDT.                      08020000
           MOVE SPACES                TO XCPT-P01-TRAN-SEQ.             08030000
           MOVE SPACES                TO XCPT-P01-PROD-CODE.            08040000
           MOVE SPACES                TO XCPT-P01-TDATE-YYYY.           08050000
           MOVE SPACES                TO XCPT-P01-TDATE-MM.             08060000
           MOVE SPACES                TO XCPT-P01-TDATE-DD.             08070000
           MOVE SPACES                TO XCPT-P01-VDATE-YYYY.           08080000
           MOVE SPACES                TO XCPT-P01-VDATE-MM.             08090000
           MOVE SPACES                TO XCPT-P01-VDATE-DD.             08100000
           MOVE SPACES                TO XCPT-P01-STM-CODE.             08110000
           MOVE SPACES                TO XCPT-P01-CHN-CODE.             08120000
           MOVE SPACES                TO XCPT-P01-TRAN-TYPE.            08130000
           MOVE ZEROS                 TO XCPT-P01-TRAN-AMT.             08140000
           MOVE SPACES                TO XCPT-P01-LINK-KEY.             ********
           MOVE SPACES                TO XCPT-P01-S1-BAT-REF.           ********
           PERFORM VARYING I FROM 1 BY 1 UNTIL I > 20000                ********
              MOVE ZEROS            TO XCPT-P02-SEQ-NO(I)               ********
              MOVE SPACES           TO XCPT-P02-BEN-NAME(I)             ********
              MOVE SPACES           TO XCPT-P02-BEN-ACCT(I)             ********
              MOVE SPACES           TO XCPT-P02-BEN-BANK-CODE(I)        ********
              MOVE ZEROS            TO XCPT-P02-NET-AMT(I)              ********
              MOVE SPACES           TO XCPT-P02-CHQ-NO(I)               ********
              MOVE SPACES           TO XCPT-P02-STATUS(I)               ********
              MOVE SPACES           TO XCPT-P02-ERROR-CODE(I)           ********
              MOVE SPACES           TO XCPT-P02-CUST-REF(I)             ********
           END-PERFORM.                                                 ********
                                                                        ********
       2020-EXIT. EXIT.                                                 ********
      *-------------------------------------------------------*         ********
       2200-READ-STMTINF-RTN.                                           ********
                                                                        ********
           READ STMT-IN-FL NEXT AT END                                  ********
                SET EOF-STMTINF  TO TRUE                                08340000
                GO TO 2200-EXIT.                                        08350000
           IF STMT-IN-STAT NOT = '00' AND                               08360000
              STMT-IN-STAT NOT = '10'                                   08370000
              DISPLAY '*** PROGRAM STMMCG05 ***'  UPON CONSOLE          08380045
              DISPLAY 'READ STMTINF IN ERROR,CODE = ' STMT-IN-STAT      08390000
                                                  UPON CONSOLE          08400000
              DISPLAY 'READ STMTINF IN ERROR,CODE = ' STMT-IN-STAT      08410000
              SET EOF-STMTINF TO TRUE                                   08420000
           END-IF.                                                      08430000
                                                                        08430100
       2200-EXIT. EXIT.                                                 08450000
      *-------------------------------------------------------*         08620000
       2400-READ-DRBTIOF-RTN.                                           08630000
                                                                        08640000
           READ DRBT-IO-FL NEXT AT END                                  08650000
                SET EOF-STMTDET   TO TRUE                               08660000
                GO TO 2400-EXIT.                                        08670000
           IF DRBT-IO-STAT NOT = '00' AND                               08680000
              DRBT-IO-STAT NOT = '10'                                   08690000
              DISPLAY '*** PROGRAM STMMCG05 ***'  UPON CONSOLE          08700045
              DISPLAY 'READ DRBTIOF ERROR,CODE = ' DRBT-IO-STAT         08710000
                                                  UPON CONSOLE          08720000
              DISPLAY 'READ DRBTIOF ERROR,CODE = ' DRBT-IO-STAT         08730000
              SET EOF-STMTDET  TO TRUE                                  08740000
           END-IF.                                                      08750000
     ***    IF EWT-TRANS                                                08750178
     ***      DISPLAY 'EWT : DRBT-CNTL-REC READNEXT =>' DRBT-CNTL-REC   08750278
     ***    END-IF.                                                     08750378
                                                                        08750400
       2400-EXIT. EXIT.                                                 08770000
      *-------------------------------------------------------*         08780000
       2500-READ-CRDTIOF-RTN.                                           08790000
                                                                        08800000
           READ CRDT-IO-FL NEXT AT END                                  08810000
                SET EOF-STMTDET   TO TRUE                               08820000
                GO TO 2500-EXIT.                                        08830000
           IF CRDT-IO-STAT NOT = '00' AND                               08840000
              CRDT-IO-STAT NOT = '10'                                   08850000
              DISPLAY '*** PROGRAM STMMCG05 ***'  UPON CONSOLE          08860045
              DISPLAY 'READ CRDTIOF ERROR,CODE = ' CRDT-IO-STAT         08870000
                                                  UPON CONSOLE          08880000
              DISPLAY 'READ CRDTIOF ERROR,CODE = ' CRDT-IO-STAT         08890000
              SET EOF-STMTDET  TO TRUE                                  08900000
           END-IF.                                                      08910000
                                                                        08910100
       2500-EXIT. EXIT.                                                 08930000
      *-------------------------------------------------------*         08940000
       2600-START-DRBTIOF.                                              08950000
            START DRBT-IO-FL KEY IS GREATER THAN                        08960000
                  DRBT-CNTL-REC                                         08970000
            INVALID KEY                                                 08980000
               DISPLAY 'THERE IS NO DEBIT STATEMENT FOR REF. '          08990000
                   DRBT-KEY-CUST-REF                                    09000000
               SET NOT-FOUND-STMTDET TO TRUE.                           09010000
            IF NOT-FOUND-STMTDET AND                                    09021002
               (ACCT-STMT2-FREQ = 'D00')                                09030002
               SET FOUND-XCPTOUTF TO TRUE                               09040000
            END-IF.                                                     09050000
      **    IF EWT-TRANS                                                09051078
      **      DISPLAY 'EWT : DRBT-CNTL-REC START READ =>' DRBT-CNTL-REC 09056078
      **    END-IF.                                                     09058078
       2600-EXIT. EXIT.                                                 09060000
      *-------------------------------------------------------*         09070000
       2700-START-CRDTIOF.                                              09080000
            START CRDT-IO-FL KEY IS GREATER THAN                        09090000
                  CRDT-CNTL-REC                                         09100000
            INVALID KEY                                                 09110000
              DISPLAY 'THERE IS NO CREDIT STATEMENT FOR REF. '          09120000
                    CRDT-KEY-CUST-REF                                   09130000
              SET NOT-FOUND-STMTDET TO TRUE.                            09140000
            IF NOT-FOUND-STMTDET AND                                    09151002
               (ACCT-STMT2-FREQ = 'D00')                                09160002
               SET FOUND-XCPTOUTF TO TRUE                               09170000
            END-IF.                                                     09180000
       2700-EXIT. EXIT.                                                 09190000
      *-------------------------------------------------------*         09200000
       2800-DEFAULT-OUTPUT.                                             09210000
           IF STMT-IN-REC-TYPE = '2'                                    09220000
              MOVE STMT-IN-TAMT            TO SWK-STMT-TAMT-ERP         09230000
              MOVE STMT-IN-CRDR            TO SWK-STMT-CRDR-ERP         09240000
              MOVE '00/00/00 00:00:00    ' TO SWK-TRANS-DATE            09250000
              MOVE '0000000000000.00'      TO SWK-TRANS-AMT             09260000
              MOVE '0000000000000.00'      TO SWK-DB-AMT                09270000
              MOVE  0000000000000.00       TO SWK-CR-AMT                09280000
              MOVE '0000000000000.00'      TO SWK-CR-NET-AMT            09290000
              MOVE '0000000000000.00'      TO SWK-CR-BENE-FEE           09300000
              MOVE '0000000000000.00'      TO SWK-TOT-WHT-AMT           09310000
              MOVE '0000000000000.00'      TO SWK-TOT-INV-AMT           09320000
              MOVE '00000000'              TO SWK-CHQ-NO                09330000
           END-IF.                                                      09340000
       2800-EXIT. EXIT.                                                 09350000
      *-------------------------------------------------------*         09360000
       2900-DEFAULT-CR.                                                 09370000
                                                                        09380000
           INITIALIZE   SWK-CR-SEQ                                      09390000
                        SWK-CR-ACCT                                     ********
                        SWK-CR-NET-AMT                                  ********
                        SWK-CR-BENE-FEE                                 ********
                        SWK-TRANS-STATUS                                ********
                        SWK-TRANS-DES                                   ********
                        SWK-CR-CURRENCY                                 ********
                        SWK-RECEIVE-BANK-CD                             ********
                        SWK-RECEIVE-BANK-NAME                           ********
                        SWK-RECEIVE-BRC-CD                              ********
                        SWK-RECEIVE-BRC-NAME                            ********
                        SWK-WHT-PRESENT                                 ********
                        SWK-INV-DET-PRESENT                             ********
                        SWK-CR-ADV-REQUIRE                              ********
                        SWK-DELIVERY-MODE                               ********
                        SWK-PICKUP-LOCATION                             ********
                        SWK-WHT-FORM-TYPE                               ********
                        SWK-WHT-TAX-NO                                  ********
                        SWK-WHT-ATT-NO                                  ********
                        SWK-NO-WHT-DET                                  ********
                        SWK-TOT-WHT-AMT                                 09590000
                        SWK-NO-INV-DET                                  09600000
                        SWK-TOT-INV-AMT                                 09610000
                        SWK-WHT-PAY-TYPE                                09620000
                        SWK-WHT-REMARK                                  09630000
                        SWK-WHT-DEDUCT-CODE                             09640000
                        SWK-BENE-NOTIFI                                 09650000
                        SWK-CUST-REF-NO                                 09660000
                        SWK-CHQ-REF-DOC-TYPE                            09670000
                        SWK-PAY-TYPE-CODE                               09680000
                        SWK-SERVICE-TYPE                                09690000
                        SWK-PAYEE-NAME-TH                               09700000
                        SWK-PAYEE-NAME-EN                               09710000
                        SWK-BENE-TAX-ID                                 09720000
                        SWK-CHQ-NO                                      09730000
                        SWK-WHT-SERIAL-NO                               09740000
                        SWK-FILLER                                      09750000
                      REPLACING NUMERIC BY ZEROES                       09760000
                                ALPHANUMERIC BY SPACES.                 09770000
           MOVE '0000000000000.00' TO SWK-CR-NET-AMT.                   09780000
           MOVE '0000000000000.00' TO SWK-CR-BENE-FEE.                  09790000
           MOVE '0000000000000.00' TO SWK-TOT-WHT-AMT.                  09800000
           MOVE '0000000000000.00' TO SWK-TOT-INV-AMT.                  09810000
           MOVE '00000000' TO SWK-CHQ-NO.                               09820000
           MOVE SPACE TO SWK-TRANS-STATUS.                              09830000
       2900-EXIT. EXIT.                                                 09840000
      *-------------------------------------------------------*         09850000
       3000-SORT-INPUT-RTN.                                             09860000
                                                                        09870000
           INITIALIZE SORT-WORK-REC                                     09880000
                      WK-HEAD-IN                                        09890000
                      WK-FOOT-IN                                        09900000
                      REPLACING NUMERIC BY ZEROES                       09910000
                                ALPHANUMERIC BY SPACES.                 09920000
           SET NOT-EOF-STMTINF     TO TRUE.                             09930000
           SET FOUND-STMTINF       TO TRUE.                             09940000
           SET NOT-CHANGE-STMTINF  TO TRUE.                             09950000
           MOVE ZERO               TO WK-CNV-DRTOT                      09960000
                                      WK-CNV-CRTOT                      09970000
                                      WK-CNV-TCOUNT.                    09980000
           MOVE ACCT-NO            TO STMT-KEY-IN-ACCT-NO.              09990002
           MOVE '000000'           TO STMT-KEY-IN-SEQ.                  10000000
           START STMT-IN-FL KEY IS GREATER THAN STMT-CNTL-REC           10010000
              INVALID KEY                                               10020000
              DISPLAY 'THERE IS NO STATEMENT TRANSACTION FOR THIS A/C ' 10030000
                       STMT-KEY-IN-ACCT-NO                              10040000
              SET NOT-FOUND-STMTINF TO TRUE                             10050000
              GO TO 3000-EXIT.                                          10060000
           PERFORM 2200-READ-STMTINF-RTN THRU 2200-EXIT.                10070000
           IF ACCT-NO NOT = STMT-KEY-IN-ACCT-NO                         10080002
              SET NOT-FOUND-STMTINF TO TRUE                             10090000
              GO TO 3000-EXIT.                                          10100000
      *    ***TO CONTINUE IN CASE STATEMENT FOUND                       10110000
           PERFORM UNTIL CHANGE-STMTINF OR EOF-STMTINF                  10120000
              INITIALIZE SWK-STMT-DET-REC                               10130000
                         REPLACING NUMERIC BY ZEROES                    10140000
                                   ALPHANUMERIC BY SPACES               10150000
              SET NOT-CHANGE-STMTDET TO TRUE                            10160000
              MOVE ZEROS             TO WK-STMT-NEW-TAMT                10170000
              PERFORM 2800-DEFAULT-OUTPUT THRU 2800-EXIT                10180000
      *>> R59060091                                                     10190000
      *       MOVE SPACES                 TO WK-HEAD-IN                 10200000
      *                                      WK-FOOT-IN                 10210000
      *<< R59060091                                                     10220000
              MOVE ACCT-IN-CORP-ID        TO SWK-IN-CORP-ID             10230000
              MOVE STMT-IN-ACCT-NO        TO SWK-IN-ACCTNO              10240000
                                                                        10250000
              IF STMT-IN-REC-TYPE  = '1'                                10260000
                 MOVE SPACES              TO WK-HEAD-IN                 10261000
                 ADD  1                   TO SWK-IN-TSEQ-NO             10270000
                 MOVE STMT-DETL-REC       TO WK-HEAD-IN                 10280000
                 MOVE WK-HEAD-IN          TO SWK-DETL-REC               10290000
                 COMPUTE  WS-STMT-TOTAL-CR   =  ZEROS                   10291000
                 COMPUTE  WS-STMT-TOTAL-DR   =  ZEROS                   10292000
                                                                        10300000
      *>> R59060091                                                     10301000
      *          RELEASE SORT-WORK-REC                                  10310000
      *<< R59060091                                                     10311000
                                                                        10320000
                 COMPUTE WK-TMP-H-OBAL =                                10330000
                        FUNCTION NUMVAL-C(WK-H-OPEN-BAL)                10340000
                                                                        10350000
                 IF WK-H-OPEN-BAL-TYP   = 'D'                           10360000
                    COMPUTE WK-STR-STRBAL  = WK-TMP-H-OBAL-9 * -1       10370000
                    MOVE    WK-STR-STRBAL  TO WK-CNV-STRBAL-AMT         10380000
                 ELSE                                                   10390000
                    COMPUTE WK-STR-STRBAL  = WK-TMP-H-OBAL-9 * 1        10400000
                    MOVE    WK-STR-STRBAL  TO WK-CNV-STRBAL-AMT         10410000
                 END-IF                                                 10420000
                 MOVE WK-STR-STRBAL      TO WK-PREV-IN-BAMT             10430000
              END-IF                                                    10440000
                                                                        10450000
              IF STMT-IN-REC-TYPE  = '3'                                10460000
                 MOVE SPACES              TO WK-FOOT-IN                 10460100
                 ADD  1                   TO SWK-IN-TSEQ-NO             10461000
                 MOVE STMT-DETL-REC       TO WK-FOOT-IN                 10470000
                 MOVE SWK-IN-TSEQ-NO      TO WK-T-TOTAL-ITEM            10471000
                 MOVE WK-FOOT-IN          TO SWK-DETL-REC               10480000
                 MOVE SWK-IN-TSEQ-NO      TO SWK-STMT-SERIAL-NO         10500000
                 RELEASE SORT-WORK-REC                                  10510000
                                                                        10510100
      *>> R59060091                                                     10510200
      *   GEN HEADER                                                    10510300
                 COMPUTE SWK-IN-TSEQ-NO  = SWK-IN-TSEQ-NO - 2           10510400
                 MOVE SWK-IN-TSEQ-NO      TO WK-H-TOTAL-DITEM           10510500
                 MOVE WK-H-ACCNUM         TO WS-INP-ACCT-NO             10510600
                 MOVE WS-INP-ACCT-BRN     TO WS-ACCT-BRN                10510700
                 MOVE WS-INP-ACCT-TYP     TO WS-ACCT-TYPE               10510800
                 IF  WS-B1K-ACCT                                        10510927
                     IF  WS-B1K-SV-ACCT                                 10511027
                         MOVE  WS-SV-ACCT-DESC    TO WK-H-ACC-TYPE      10511127
                     END-IF                                             10511227
                     IF  WS-B1K-FX-ACCT                                 10511327
                         MOVE  WS-FX-ACCT-DESC    TO WK-H-ACC-TYPE      10511427
                     END-IF                                             10511527
                     IF  WS-B1K-LT-ACCT                                 10511627
                         MOVE  WS-LT-ACCT-DESC    TO WK-H-ACC-TYPE      10511727
                     END-IF                                             10511827
                     IF  WS-B1K-CA-ACCT                                 10511927
                         MOVE  WS-CA-ACCT-DESC    TO WK-H-ACC-TYPE      10512027
                     END-IF                                             10512127
                 ELSE                                                   10512227
                     IF  WS-SV-ACCT                                     10512327
                         MOVE  WS-SV-ACCT-DESC    TO WK-H-ACC-TYPE      10512427
                     END-IF                                             10512527
                     IF  WS-FX-ACCT                                     10512627
                         MOVE  WS-FX-ACCT-DESC    TO WK-H-ACC-TYPE      10512727
                     END-IF                                             10512827
                     IF  WS-LT-ACCT                                     10512927
                         MOVE  WS-LT-ACCT-DESC    TO WK-H-ACC-TYPE      10513027
                     END-IF                                             10513127
                     IF  WS-CA-ACCT                                     10513227
                         MOVE  WS-CA-ACCT-DESC    TO WK-H-ACC-TYPE      10513327
                     END-IF                                             10513427
                 END-IF                                                 10513527
     ***         IF  WS-B1K-SV-ACCT OR WS-SV-ACCT                       10513627
     ***             MOVE  WS-SV-ACCT-DESC    TO WK-H-ACC-TYPE          10513727
     ***         END-IF                                                 10513827
     ***         IF  WS-B1K-FX-ACCT OR WS-FX-ACCT                       10513927
     ***             MOVE  WS-FX-ACCT-DESC    TO WK-H-ACC-TYPE          10514027
     ***         END-IF                                                 10514127
     ***         IF  WS-B1K-LT-ACCT OR WS-LT-ACCT                       10514227
     ***             MOVE  WS-LT-ACCT-DESC    TO WK-H-ACC-TYPE          10514327
     ***         END-IF                                                 10514427
     ***         IF  WS-B1K-CA-ACCT OR WS-CA-ACCT                       10514527
     ***             MOVE  WS-CA-ACCT-DESC    TO WK-H-ACC-TYPE          10514627
     ***         END-IF                                                 10514727
                 MOVE 1                   TO SWK-IN-TSEQ-NO             10514827
                 MOVE 'THB'               TO WK-H-CCY                   10514927
                 MOVE WS-STMT-TOTAL-CR    TO WK-H-CR-TOTAL              10515027
                 MOVE 'C'                 TO WK-H-CR-TOTAL-TYP          10515127
                 MOVE WS-STMT-TOTAL-DR    TO WK-H-DR-TOTAL              10515227
                 MOVE 'D'                 TO WK-H-DR-TOTAL-TYP          10515327
                 MOVE WK-T-CLS-BAL        TO WK-H-CLS-BAL               10515427
                 MOVE WK-T-CLS-BAL-TYP    TO WK-H-CLS-BAL-TYP           10515527
                 MOVE WK-HEAD-IN          TO SWK-DETL-REC               10515627
                 RELEASE SORT-WORK-REC                                  10515727
      *<< R59060091                                                     10515827
                                                                        10516027
              END-IF                                                    10520000
                                                                        10530000
              IF STMT-IN-REC-TYPE  = '2'                                10540000
                 IF STMT-IN-CRDR = 'D '                                 10550000
                    ADD WK-STMT-IN-TAMT      TO WK-CNV-DRTOT            10560000
      *>> R59060091                                                     10561000
                    COMPUTE WS-STMT-TOTAL-DR =                          10561100
                               WS-STMT-TOTAL-DR + WK-STMT-IN-TAMT       10561200
      **            IF STMT-IN-ACCT-NO = '1113001658'                   10561300
      **               DISPLAY 'WS-STMT-TOTAL-DR  :' WS-STMT-TOTAL-DR   10561400
      **               DISPLAY 'WK-STMT-IN-TAMT   :' WK-STMT-IN-TAMT    10561500
      **            END-IF                                              10561600
      *<< R59060091                                                     10562000
                 ELSE                                                   10570000
      ***           ***TRAN TYPE IS 'C '                                10580000
                    ADD WK-STMT-IN-TAMT      TO WK-CNV-CRTOT            10590000
      *>> R59060091                                                     10591000
                    COMPUTE WS-STMT-TOTAL-CR =                          10592000
                               WS-STMT-TOTAL-CR + WK-STMT-IN-TAMT       10593000
      **            IF STMT-IN-ACCT-NO = '1113001658'                   10593100
      **               DISPLAY 'WS-STMT-TOTAL-CR  :' WS-STMT-TOTAL-CR   10593200
      **               DISPLAY 'WK-STMT-IN-TAMT   :' WK-STMT-IN-TAMT    10593300
      **            END-IF                                              10593400
      *<< R59060091                                                     10594000
                 END-IF                                                 10600000
      *          MOVE 'D'                    TO SWK-REC-TYPE            10610000
                 MOVE STMT-DETL-REC          TO SWK-STMT-REC            10620000
                 MOVE STMT-DETL-REC2         TO SWK-STMT-REC2           10620206
      *>> UR58080051                                                    10621000
                 IF ((STMT-IN-DESC(6:3) = 'PAY' OR 'VAL') AND           10622000
                      STMT-IN-DESC(15:3) = 'SCG')                       10623000
                      SET NOT-GEN-DETAIL TO TRUE                        10624000
      *>> CB61010008                                                    10624332
      *          ELSE                                                   10625020
                 ELSE IF (STMT-IN-DESC(6:3) = 'PAY' OR                  10625220
                                              'PA2' OR 'PA3' OR         10625320
                                              'PA4' OR 'PA5' OR         10625420
                                              'PA5' OR 'PA6' )          10625520
                           SET NOT-GEN-DETAIL TO TRUE                   10625620
                      ELSE                                              10626021
      *<< CB61010008                                                    10626120
                           SET GEN-DETAIL TO TRUE                       10626221
                      END-IF                                            10627021
                 END-IF                                                 10627121
      *<< UR58080051                                                    10628000
      *>> SR-22493                                                      10628189
      *          IF  (STMT-IN-DESC(1:4 ) = 'EWT ')                      10628289
      *               SET EWT-TRANS       TO TRUE                       10628389
                 IF  (STMT-IN-DESC(2:3 ) = 'EWT')                       10628483
                      SET EWT-TRANS       TO TRUE                       10628583
      *<< SR-22493                                                      10628689
                 ELSE                                                   10628783
                      SET NOT-EWT-TRANS   TO TRUE                       10628883
                 END-IF                                                 10628983
      *>> UR58080051                                                    10629020
                 IF (STMT-IN-CHAN        = 'BCMS' AND                   10630000
                     STMT-IN-SYMBOL  NOT = 'FE '  AND                   10640000
                    ((STMT-IN-DESC(2:3)  = 'MCP' OR 'MCL' OR 'XMQ' OR   10650053
                                           'DCP' OR 'DDP' OR 'XDQ' OR   10660053
                                           'MC2' OR 'BNT' ) OR          10671053
                                           (EWT-TRANS)))                10671154
      *>> UR58080051                                                    10672000
                 AND GEN-DETAIL                                         10673000
      *<< UR58080051                                                    10674000
      *       ***ONLY SEPERATE STATEMENT DETAIL AS FOLLOW               10680000
      *       ***   CHANNEL 'BCMS' AND TRAN TYPE 'D' OR 'C' AND         10690000
      *       ***   PRODUCT = 'MCP' OR 'MCL' OR 'XMQ' OR 'DCP' OR       10700000
      *       ***             'DDP' OR 'XDQ' OR 'MC2' OR 'BNT'          10711000
                    IF ( GEN-PYMT-STMTINF AND                           10720002
                      ((STMT-IN-DESC(2:3)   = 'MCP' OR 'MCL' OR 'XMQ' OR10730053
                                              'DCP' OR 'DDP' OR 'XDQ' OR10740053
                                              'MC2' OR 'BNT' ) OR       10752053
                                           (EWT-TRANS)))                10753054
                        PERFORM 3300-GEN-STMT-DETAIL THRU 3300-EXIT     10760000
                    ELSE                                                10770000
                       ADD 1                    TO SWK-IN-TSEQ-NO       10780000
                                                   WK-CNV-TCOUNT        10790000
                       MOVE SWK-IN-TSEQ-NO      TO SWK-STMT-SERIAL-NO   10800000
                                                                        10810000
                       PERFORM 4200-SUM-PREV-BAMT THRU 4200-EXIT        10819000
                                                                        10820000
                       RELEASE SORT-WORK-REC                            10830000
                    END-IF                                              10840000
                 ELSE                                                   10850000
      *---------------------------------------------------------        10860000
      *          (STMT-IN-CHAN      NOT = 'BCMS'           ) OR         10870000
      *          (STMT-IN-CHAN          = 'BCMS' AND                    10880000
      *           STMT-IN-SYMBOL        = 'FE '            ) OR         10890000
      *          (STMT-IN-CHAN          = 'BCMS' AND                    10900000
      *           STMT-IN-SYMBOL    NOT = 'FE '  AND                    10910000
      *           STMT-IN-DESC(2:3) NOT = 'MCP'  AND 'MCL')             10920000
      *---------------------------------------------------------        10930000
                    <USER> <GROUP>                    TO SWK-IN-TSEQ-NO          10940000
                                                WK-CNV-TCOUNT           10950000
                    MOVE SWK-IN-TSEQ-NO      TO SWK-STMT-SERIAL-NO      10960000
                    IF (STMT-IN-CHAN      = 'SYSG' AND                  10970000
                       STMT-IN-CRDR      = 'C '    AND                  10980000
                       STMT-IN-DESC(1:3)    = 'MCP' OR 'XMQ') OR        10990000
                       (STMT-IN-CHAN        = 'DTC' AND                 11000000
                       STMT-IN-CRDR         = 'C '   AND                11010000
                       STMT-IN-DESC(1:3)    = 'DDP' OR 'XDQ')           11020000
                       IF GEN-PYMT-STMTINF                              11022002
                          IF STMT-IN-DESC(1:3) = 'DDP' OR 'XDQ' THEN    11030000
                             MOVE STMT-IN-DESC(9:8)  TO SWK-CHQ-NO      11040000
                             MOVE STMT-IN-DESC(1:3)  TO SWK-PROD-CODE   11050000
                             IF STMT-IN-DESC(5:4) = 'PDST' THEN         11060000
                                MOVE 'S' TO SWK-TRANS-STATUS            11070000
                                MOVE 'CHQ STOPPED' TO SWK-TRANS-DES     11080000
                             END-IF                                     11090000
                          ELSE                                          11100000
      *>> CB62090005                                                    11101032
      *                      MOVE '0'               TO SWK-CHQ-NO(1:1)  11110032
      *                      MOVE STMT-IN-DESC(5:7) TO SWK-CHQ-NO(2:7)  11120032
      *                      MOVE STMT-IN-DESC(1:3) TO SWK-PROD-CODE    11130032
      *                      IF STMT-IN-DESC(12:4) = 'PDRJ' THEN        11140032
      *                                                                 11141032
                             MOVE STMT-IN-DESC(5:8) TO SWK-CHQ-NO       11141132
                             MOVE STMT-IN-DESC(1:3) TO SWK-PROD-CODE    11141232
                             IF STMT-IN-DESC(13:4) = 'PDRJ' THEN        11142032
      *<< CB62090005                                                    11143032
                                MOVE 'R' TO SWK-TRANS-STATUS            11150000
                                MOVE 'CHQ REJECTED' TO SWK-TRANS-DES    11160000
                             ELSE                                       11170000
      *>> CB62090005                                                    11180132
      **                        IF STMT-IN-DESC(12:4) = 'PDST' THEN     11180232
                                IF STMT-IN-DESC(13:4) = 'PDST' THEN     11180332
      *<< CB62090005                                                    11181032
                                 MOVE 'S' TO SWK-TRANS-STATUS           11190000
                                 MOVE 'CHQ STOPPED' TO SWK-TRANS-DES    11200000
                                END-IF                                  11210000
                             END-IF                                     11211000
                          END-IF                                        11220000
                       END-IF                                           11230000
                    END-IF                                              11240000
                    PERFORM 4200-SUM-PREV-BAMT THRU 4200-EXIT           11249200
                    RELEASE SORT-WORK-REC                               11250000
                  END-IF                                                11260000
              END-IF                                                    11270000
              PERFORM 2200-READ-STMTINF-RTN THRU 2200-EXIT              11280000
              IF ACCT-NO NOT = STMT-KEY-IN-ACCT-NO                      11290002
                 SET CHANGE-STMTINF TO TRUE                             11300000
              ELSE                                                      11310000
                 IF NOT-EOF-STMTINF                                     11320000
                    COMPUTE WK-STMT-IN-TAMT = STMT-IN-TAMT              11340000
                    COMPUTE WK-STMT-IN-BAMT = STMT-IN-BAMT              11350000
                 END-IF                                                 11450000
              END-IF                                                    11451000
           END-PERFORM.                                                 11460000
           IF CHANGE-STMTINF OR EOF-STMTINF                             11470000
              MOVE WK-STMT-IN-BAMT  TO WK-CNV-ENDBAL                    11480000
              MOVE WK-CNV-TCOUNT    TO WK-T-SERIAL-NO                   11490000
           END-IF.                                                      11510000
                                                                        11520000
       3000-EXIT. EXIT.                                                 11530000
      *-------------------------------------------------------*         11540000
       3100-STMTDET-RTN.                                                11550000
                                                                        11560000
           MOVE ZEROS              TO TOT-CRDT.                         11570000
           SET CRDT-IDX            TO 1.                                11580000
           PERFORM UNTIL CHANGE-STMTDET OR EOF-STMTDET                  11590000
              INITIALIZE SWK-STMT-DET-REC                               11600000
                         REPLACING NUMERIC BY ZEROES                    11610000
                                   ALPHANUMERIC BY SPACES               11620000
                                                                        11620100
      *>> CB63060014                                                    11620239
      *>> ------------------------------------------------              11620359
      *>> START ASSIGN BNT ROUTINE                                      11620459
      *>> ------------------------------------------------              11620559
              <USER> <GROUP>  TO TRUE                           11620641
                                                                        11620759
              IF STMT-IN-DESC(2:3) = 'BNT'                              11620859
                 SET USE-BNT-ROUTINE TO TRUE                            11620959
              END-IF                                                    11621059
                                                                        11621159
              IF STMT-IN-DESC(2:3)  = 'DCP' AND                         11621259
                 STMT-IN-DESC(6:1)  = 'H'   AND                         11621359
                 STMT-IN-DESC(11:3) = 'BNT'                             11621459
                 SET USE-BNT-ROUTINE TO TRUE                            11621559
              END-IF                                                    11621659
      *<< ------------------------------------------------              11621959
      *<< END ASSIGN BNT ROUTINE                                        11622059
      *<< ------------------------------------------------              11622159
                                                                        11622259
      *>> ------------------------------------------------              11622359
      *>> START ASSIGN EWT-BNT ROUTINE                                  11622459
      *>> ------------------------------------------------              11622559
              <USER> <GROUP>  TO TRUE                           11622659
                                                                        11622759
      *>> SR-22493                                                      11622889
      *        IF STMT-IN-DESC(1:3) = 'EWT' AND                         11622989
      *          STMT-IN-DESC(5:3) = 'BNT'                              11623089
              IF STMT-IN-DESC(2:3) = 'EWT' AND                          11623183
                 STMT-IN-DESC(6:3) = 'BNT'                              11623283
      *<< SR-22493                                                      11623389
                 SET USE-EWT-BNT-RTN TO TRUE                            11623483
              END-IF                                                    11623583
                                                                        11623683
      *>> SR-22493                                                      11623789
      *       IF STMT-IN-DESC(1:3)  = 'EWT' AND                         11623889
      *          STMT-IN-DESC(5:3)  = 'DCP' AND                         11623989
      *          STMT-IN-DESC(9:1)  = 'H'   AND                         11624089
      *          STMT-IN-DESC(14:3) = 'BNT'                             11624189
              IF STMT-IN-DESC(2:3)  = 'EWT' AND                         11624283
                 STMT-IN-DESC(6:3)  = 'DCP' AND                         11624383
                 STMT-IN-DESC(10:1)  = 'H'   AND                        11624483
                 STMT-IN-DESC(15:3) = 'BNT'                             11624583
      *<< SR-22493                                                      11624689
                 SET USE-EWT-BNT-RTN TO TRUE                            11624783
              END-IF                                                    11624883
      *<< ------------------------------------------------              11624983
      *<< END ASSIGN EWT-BNT ROUTINE                                    11625083
      *<< ------------------------------------------------              11625183
                                                                        11625283
      *>> ------------------------------------------------              11625383
      *>> START-PRCESS BNT ROUTINE                                      11625483
      *>> ------------------------------------------------              11625583
              <USER> <GROUP>                                        11625683
      *<< CB63060014                                                    11625783
                 SET N-BNT-DET-FND TO TRUE                              11625883
                 PERFORM 3500-BNT-CHECK-PROCESS-RECORD THRU 3500-EXIT   11625983
                                  UNTIL CHANGE-STMTDET OR EOF-STMTDET   11626083
                 IF N-BNT-DET-FND                                       11626183
                    ADD 1                  TO SWK-IN-TSEQ-NO            11626283
                                              WK-CNV-TCOUNT             11626383
                    MOVE SWK-IN-TSEQ-NO    TO SWK-STMT-SERIAL-NO        11626483
                    MOVE 'W'               TO SWK-TRANS-STATUS          11626583
                    MOVE 'ERP035:Not Found BNT or DCP Transaction in CN'11626683
                                            TO SWK-TRANS-DES            11626783
                    PERFORM 4200-SUM-PREV-BAMT THRU 4200-EXIT           11626883
                    RELEASE SORT-WORK-REC                               11626983
                    GO TO 3100-EXIT                                     11627083
                 END-IF                                                 11627183
              END-IF                                                    11627283
      *<< ------------------------------------------------              11627383
      *<< END-PRCESS BNT ROUTINE                                        11627483
      *<< ------------------------------------------------              11627583
                                                                        11627683
      *>> ------------------------------------------------              11627783
      *>> START-PRCESS EWT-BNT ROUTINE                                  11627883
      *>> ------------------------------------------------              11627983
              <USER> <GROUP>                                        11628083
                 SET N-BNT-DET-FND TO TRUE                              11628159
                 PERFORM 3700-EWT-BNT-CHECK-PROCESS-REC THRU 3700-EXIT  11629059
                                  UNTIL CHANGE-STMTDET OR EOF-STMTDET   11630059
                 IF N-BNT-DET-FND                                       11631059
                    ADD 1                  TO SWK-IN-TSEQ-NO            11631159
                                              WK-CNV-TCOUNT             11631259
                    MOVE SWK-IN-TSEQ-NO    TO SWK-STMT-SERIAL-NO        11631359
                    MOVE 'W'               TO SWK-TRANS-STATUS          11631459
                    MOVE 'ERP035:Not Found BNT or DCP Transaction in CN'11631559
                                            TO SWK-TRANS-DES            11631659
                    PERFORM 4200-SUM-PREV-BAMT THRU 4200-EXIT           11631759
                    RELEASE SORT-WORK-REC                               11631859
                    GO TO 3100-EXIT                                     11631959
                 END-IF                                                 11632059
              END-IF                                                    11632159
      *<< ------------------------------------------------              11632259
      *<< END-PRCESS EWT-BNT ROUTINE                                    11632359
      *<< ------------------------------------------------              11632459
                                                                        <USER>
              <GROUP> 2800-DEFAULT-OUTPUT THRU 2800-EXIT                11633059
      *>> R59060091                                                     11640000
      *       ADD 1                   TO SWK-IN-TSEQ-NO                 11641000
      *                                  WK-CNV-TCOUNT                  11650000
      *       MOVE SWK-IN-TSEQ-NO     TO SWK-STMT-SERIAL-NO             11660000
      *<< R59060091                                                     11660100
              IF STMT-IN-CRDR = 'D '                                    11670000
      *>> R59060091                                                     11671000
      *          IF DRBT-CR-STATUS = 'O'                                11680000
                 IF DRBT-CR-STATUS = 'I' OR 'J' OR 'O'                  ********
                    MOVE  'O' TO DRBT-CR-STATUS                         ********
      *<< R59060091                                                     ********
                    COMPUTE WK-CURR-TRANS-AMT =                         ********
                            FUNCTION NUMVAL(DRBT-CR-AMT)                ********
                                                                        ********
      *       *** BNT Record AMT - Fee Charge Receiveing Bank           ********
      *>> CB63060014                                                    ********
      *             IF STMT-IN-DESC(2:3) = 'BNT'                        ********
                    IF USE-BNT-ROUTINE                                  ********
      *             IF USE-BNT-ROUTINE OR USE-EWT-BNT-RTN               ********
      *<< CB63060014                                                    ********
                       AND DRBT-CR-FEE-BNT-FLG = 'F'                    ********
                       COMPUTE WK-FEE-RECV-BANK  =                      ********
                               FUNCTION NUMVAL(DRBT-CR-BENE-TAX-ID)     ********
                       IF  WK-FEE-RECV-BANK NOT EQUAL ZEROES            ********
                           COMPUTE WK-CURR-TRANS-AMT =                  ********
                                   WK-CURR-TRANS-AMT - WK-FEE-RECV-BANK ********
                       END-IF                                           ********
                    END-IF                                              ********
      *       *** END Process                                           ********
                                                                        ********
                    ADD WK-CURR-TRANS-AMT    TO WK-STMT-NEW-TAMT        ********
                    COMPUTE WK-STMT-NEW-BAMT = WK-PREV-IN-BAMT          ********
                                             - WK-CURR-TRANS-AMT        ********
                    MOVE WK-STMT-NEW-BAMT    TO WK-CNV-NEW-BAMT         ********
                    MOVE WK-CURR-TRANS-AMT   TO SWK-STMT-TAMT-ERP       ********
                    MOVE WK-CNV-NEW-BAMT(1:14) TO SWK-STMT-BAMT(1:14)   ********
                    MOVE WK-CNV-NEW-BAMT(16:2) TO SWK-STMT-BAMT(15:2)   ********
                    MOVE DRBT-DATA-REC       TO SWK-STMT-DET-REC        ********
      *>> R59060091                                                     11781000
                    ADD  1                   TO SWK-IN-TSEQ-NO          11782000
                                                WK-CNV-TCOUNT           11783000
                    MOVE SWK-IN-TSEQ-NO      TO SWK-STMT-SERIAL-NO      11785000
      *<< R59060091                                                     11786000
                    RELEASE SORT-WORK-REC                               ********
                    MOVE WK-STMT-NEW-BAMT    TO WK-PREV-IN-BAMT         ********
                    PERFORM 5201-MOVE-XCPTOUTF-1 THRU 5201-1-EXIT       ********
                    PERFORM 5201-MOVE-XCPTOUTF-2 THRU 5201-2-EXIT       ********
                 END-IF                                                 ********
                                                                        ********
      *       *** Record BNT_COM Fee Charge Receiveing Bank             ********
      *>> CB63060014                                                    ********
      *          IF STMT-IN-DESC(2:3) = 'BNT' AND DRBT-CR-STATUS = 'O'  ********
      *          IF USE-BNT-ROUTINE                                     ********
                 IF (USE-BNT-ROUTINE OR USE-EWT-BNT-RTN)                ********
                    AND DRBT-CR-STATUS = 'O'                            ********
      *<< CB63060014                                                    ********
                    AND DRBT-CR-FEE-BNT-FLG = 'F'                       ********
                    AND WK-FEE-RECV-BANK NOT EQUAL ZEROES               ********
                                                                        ********
                    ADD 1                   TO SWK-IN-TSEQ-NO           ********
                                               WK-CNV-TCOUNT            ********
                    MOVE SWK-IN-TSEQ-NO     TO SWK-STMT-SERIAL-NO       ********
                                                                        ********
                    COMPUTE WK-CURR-TRANS-AMT = WK-FEE-RECV-BANK        ********
                    ADD WK-CURR-TRANS-AMT    TO WK-STMT-NEW-TAMT        ********
                    COMPUTE WK-STMT-NEW-BAMT = WK-PREV-IN-BAMT          ********
                                             - WK-CURR-TRANS-AMT        ********
                    MOVE WK-STMT-NEW-BAMT    TO WK-CNV-NEW-BAMT         ********
                    MOVE WK-CURR-TRANS-AMT   TO SWK-STMT-TAMT-ERP       ********
                    MOVE WK-CNV-NEW-BAMT(1:14) TO SWK-STMT-BAMT(1:14)   ********
                    MOVE WK-CNV-NEW-BAMT(16:2) TO SWK-STMT-BAMT(15:2)   ********
                    MOVE DRBT-DATA-REC       TO SWK-STMT-DET-REC        ********
                    MOVE 'Z'                 TO SWK-TRANS-STATUS        ********
                    MOVE 'Fee BNT Receiver Bank'                        ********
                                             TO SWK-TRANS-DES           ********
                    MOVE 'COM'               TO SWK-STMT-TCODE          ********
                    MOVE 'FE '               TO SWK-STMT-SYMBOL         ********
                    RELEASE SORT-WORK-REC                               ********
                    MOVE WK-STMT-NEW-BAMT    TO WK-PREV-IN-BAMT         ********
                    PERFORM 5201-MOVE-XCPTOUTF-1 THRU 5201-1-EXIT       ********
                    PERFORM 5201-MOVE-XCPTOUTF-2 THRU 5201-2-EXIT       ********
                 END-IF                                                 ********
      *       *** End Process                                           ********
                 PERFORM 2400-READ-DRBTIOF-RTN THRU 2400-EXIT           ********
                 IF NOT-EOF-STMTDET                                     ********
                   IF EWT-TRANS                                         11851054
      *>> SR-22493                                                      11852089
      *             IF DRBT-KEY-CUST-REF NOT = STMT-IN-DESC(9:12)       11860089
                    IF DRBT-KEY-CUST-REF NOT = STMT-IN-DESC-A(10:12)    11861084
                       OR DRBT-KEY-DRBT-ACCT NOT = STMT-KEY-IN-ACCT-NO  11870000
      *                OR DRBT-KEY-PROD-CODE  NOT = STMT-IN-DESC(1:3)   11880089
      *                OR DRBT-KEY-PROD-CODE2 NOT = STMT-IN-DESC(5:3)   11881089
                       OR DRBT-KEY-PROD-CODE  NOT = STMT-IN-DESC(2:3)   11882083
                       OR DRBT-KEY-PROD-CODE2 NOT = STMT-IN-DESC(6:3)   11883083
      *<< SR-22493                                                      11884089
                       SET CHANGE-STMTDET TO TRUE                       11890000
                    END-IF                                              11900000
                   END-IF                                               11901054
                   IF NOT-EWT-TRANS                                     11902054
                    IF DRBT-KEY-CUST-REF NOT = STMT-IN-DESC(6:12)       11903054
                       OR DRBT-KEY-DRBT-ACCT NOT = STMT-KEY-IN-ACCT-NO  11904054
                       OR DRBT-KEY-PROD-CODE NOT = STMT-IN-DESC(2:3)    11905054
                       SET CHANGE-STMTDET TO TRUE                       11906054
                    END-IF                                              11907054
                   END-IF                                               11908054
                 END-IF                                                 11910000
              ELSE                                                      11920000
      *       ***STMT-IN-CRDR = 'C '                                    11930000
      *>> CB62090014                                                    11931036
      *          IF CRDT-CR-STATUS = 'C' OR 'J'                         11940036
                 IF CRDT-CR-STATUS = 'J'                                11941034
      *<< CB62090014                                                    11942036
                    IF CRDT-CR-STATUS = 'C'                             11950000
                       COMPUTE WK-CURR-TRANS-AMT =                      11960000
                               FUNCTION NUMVAL(CRDT-CR-AMT)             ********
                    ELSE                                                ********
                       COMPUTE WK-CURR-TRANS-AMT =                      ********
                               FUNCTION NUMVAL(CRDT-CR-NET-AMT)         ********
                    END-IF                                              ********
                                                                        ********
      *       *** BNT Record AMT - Fee Charge Receiveing Bank           ********
      *>> CB63060014                                                    ********
      *             IF STMT-IN-DESC(2:3) = 'BNT'                        ********
                    IF USE-BNT-ROUTINE                                  ********
      *             IF USE-BNT-ROUTINE OR USE-EWT-BNT-RTN               ********
      *<< CB63060014                                                    ********
                       AND CRDT-CR-FEE-BNT-FLG = 'F'                    ********
                       COMPUTE WK-FEE-RECV-BANK  =                      ********
                               FUNCTION NUMVAL(CRDT-CR-BENE-TAX-ID)     ********
                       IF  WK-FEE-RECV-BANK NOT EQUAL ZEROES            ********
                           COMPUTE WK-CURR-TRANS-AMT =                  ********
                                   WK-CURR-TRANS-AMT - WK-FEE-RECV-BANK ********
                       END-IF                                           ********
                    END-IF                                              ********
      *       *** END Process                                           ********
                    ADD WK-CURR-TRANS-AMT    TO WK-STMT-NEW-TAMT        ********
                    COMPUTE WK-STMT-NEW-BAMT = WK-PREV-IN-BAMT          ********
                                             + WK-CURR-TRANS-AMT        ********
                    MOVE WK-STMT-NEW-BAMT    TO WK-CNV-NEW-BAMT         ********
                    MOVE WK-CURR-TRANS-AMT   TO SWK-STMT-TAMT-ERP       ********
                    MOVE WK-CNV-NEW-BAMT(1:14) TO SWK-STMT-BAMT(1:14)   ********
                    MOVE WK-CNV-NEW-BAMT(16:2) TO SWK-STMT-BAMT(15:2)   ********
                    MOVE CRDT-DATA-REC       TO SWK-STMT-DET-REC        ********
      *>> R59060091                                                     ********
                    ADD  1                   TO SWK-IN-TSEQ-NO          12092000
                                                WK-CNV-TCOUNT           12093000
                    MOVE SWK-IN-TSEQ-NO      TO SWK-STMT-SERIAL-NO      12094000
      *<< R59060091                                                     12096000
                    RELEASE SORT-WORK-REC                               ********
                    MOVE WK-STMT-NEW-BAMT    TO WK-PREV-IN-BAMT         ********
                    PERFORM 5201-MOVE-XCPTOUTF-1 THRU 5201-1-EXIT       ********
                    PERFORM 5201-MOVE-XCPTOUTF-2 THRU 5201-2-EXIT       ********
                 END-IF                                                 ********
                                                                        ********
      *       *** Record BNT_COM Fee Charge Receiveing Bank             ********
      *>> CB63060014                                                    ********
      *          IF STMT-IN-DESC(2:3) = 'BNT'                           ********
      *          IF USE-BNT-ROUTINE                                     ********
                 IF (USE-BNT-ROUTINE OR USE-EWT-BNT-RTN)                ********
      *<< CB63060014                                                    ********
      *>> CB62090014                                                    ********
      ***           AND (CRDT-CR-STATUS = 'C' OR 'J')                   ********
                    AND (CRDT-CR-STATUS = 'J')                          ********
      *<< CB62090014                                                    ********
                    AND CRDT-CR-FEE-BNT-FLG = 'F'                       ********
                    AND WK-FEE-RECV-BANK NOT EQUAL ZEROES               ********
                                                                        ********
                    ADD 1                   TO SWK-IN-TSEQ-NO           ********
                                               WK-CNV-TCOUNT            ********
                    MOVE SWK-IN-TSEQ-NO     TO SWK-STMT-SERIAL-NO       ********
                    COMPUTE WK-CURR-TRANS-AMT = WK-FEE-RECV-BANK        ********
                    ADD WK-CURR-TRANS-AMT    TO WK-STMT-NEW-TAMT        ********
                    COMPUTE WK-STMT-NEW-BAMT = WK-PREV-IN-BAMT          ********
                                             + WK-CURR-TRANS-AMT        ********
                    MOVE WK-STMT-NEW-BAMT    TO WK-CNV-NEW-BAMT         ********
                    MOVE WK-CURR-TRANS-AMT   TO SWK-STMT-TAMT-ERP       ********
                    MOVE WK-CNV-NEW-BAMT(1:14) TO SWK-STMT-BAMT(1:14)   ********
                    MOVE WK-CNV-NEW-BAMT(16:2) TO SWK-STMT-BAMT(15:2)   ********
                    MOVE CRDT-DATA-REC       TO SWK-STMT-DET-REC        ********
                    MOVE 'Z'                 TO SWK-TRANS-STATUS        ********
                    MOVE 'Fee BNT Receiver Bank'                        ********
                                             TO SWK-TRANS-DES           ********
                    MOVE 'COM'               TO SWK-STMT-TCODE          ********
                    MOVE 'FE '               TO SWK-STMT-SYMBOL         ********
                    RELEASE SORT-WORK-REC                               ********
                    MOVE WK-STMT-NEW-BAMT    TO WK-PREV-IN-BAMT         ********
                    PERFORM 5201-MOVE-XCPTOUTF-1 THRU 5201-1-EXIT       ********
                    PERFORM 5201-MOVE-XCPTOUTF-2 THRU 5201-2-EXIT       ********
                 END-IF                                                 ********
      *       *** End Process                                           ********
                 PERFORM 2500-READ-CRDTIOF-RTN THRU 2500-EXIT           ********
                 IF NOT-EOF-STMTDET                                     ********
                   IF EWT-TRANS                                         12161054
      *>> SR-22493                                                      12162089
      *             IF (CRDT-KEY-CUST-REF NOT = STMT-IN-DESC(9:12))     12170089
                    IF (CRDT-KEY-CUST-REF NOT = STMT-IN-DESC-A(10:12))  12171084
                       OR CRDT-KEY-DRBT-ACCT  NOT = STMT-KEY-IN-ACCT-NO 12180054
      *                OR CRDT-KEY-PROD-CODE  NOT = STMT-IN-DESC(1:3)   12190089
      *                OR CRDT-KEY-PROD-CODE2 NOT = STMT-IN-DESC(5:3)   12191089
      *<< SR-22493                                                      12191189
                       OR CRDT-KEY-PROD-CODE  NOT = STMT-IN-DESC(2:3)   12192083
                       OR CRDT-KEY-PROD-CODE2 NOT = STMT-IN-DESC(6:3)   12193083
                       SET CHANGE-STMTDET TO TRUE                       12200000
                    END-IF                                              12210000
                   END-IF                                               12211054
                   IF NOT-EWT-TRANS                                     12212054
                    IF (CRDT-KEY-CUST-REF NOT = STMT-IN-DESC(6:12))     12213054
                       OR CRDT-KEY-DRBT-ACCT NOT = STMT-KEY-IN-ACCT-NO  12214054
                       OR CRDT-KEY-PROD-CODE NOT = STMT-IN-DESC(2:3)    12215054
                       SET CHANGE-STMTDET TO TRUE                       12216054
                    END-IF                                              12217054
                   END-IF                                               12218054
                 END-IF                                                 12220000
              END-IF                                                    12230000
                                                                        12231000
      ***     IF STMT-IN-DESC(2:3) NOT = 'BNT'                          12233000
              IF CHANGE-STMTDET OR EOF-STMTDET                          12240000
                 IF WK-STMT-IN-TAMT > WK-STMT-NEW-TAMT                  12250000
                    SET NOT-FOUND-STMTDET  TO TRUE                      12260000
                    IF ACCT-STMT2-FREQ = 'D00'                          12270002
                       SET FOUND-XCPTOUTF     TO TRUE                   12280000
                    END-IF                                              12290000
                    MOVE ZEROS             TO WK-STMT-NEW-BAMT          12300000
                    ADD 1                  TO SWK-IN-TSEQ-NO            12310000
                                              WK-CNV-TCOUNT             12320000
                    MOVE SWK-IN-TSEQ-NO    TO SWK-STMT-SERIAL-NO        12330000
                    COMPUTE WK-DIFF-TAMT = WK-STMT-IN-TAMT              12340000
                                         - WK-STMT-NEW-TAMT             12350000
                    MOVE WK-DIFF-TAMT      TO SWK-CR-AMT                12360000
                    MOVE WK-DIFF-TAMT      TO SWK-STMT-TAMT-ERP         12370000
                    PERFORM 2900-DEFAULT-CR THRU 2900-EXIT              12380000
                    IF EWT-TRANS                                        12381055
      *>> SR-22493                                                      12382089
      *                MOVE STMT-IN-DESC(9:12)TO SWK-CUST-REF(21:12)    12390089
                       MOVE STMT-IN-DESC-A(10:12) TO SWK-CUST-REF(21:12)12390184
      *<< SR-22493                                                      12390289
                    END-IF                                              12391055
                    IF NOT-EWT-TRANS                                    12392055
                       MOVE STMT-IN-DESC(6:12)TO SWK-CUST-REF(21:12)    12393055
                    END-IF                                              12394055
                    MOVE 'A'               TO SWK-TRANS-STATUS          12400000
                    IF SWK-STMT-CRDR = 'D '                             12410000
                       MOVE 'D '                  TO SWK-STMT-CRDR-ERP  12420000
                       MOVE 'ERP031:PMT Debit Adjust Txn'               12421003
                                                  TO SWK-TRANS-DES      12422003
                       COMPUTE WK-STMT-NEW-BAMT = WK-PREV-IN-BAMT       12430000
                                                - WK-DIFF-TAMT          12440000
                    ELSE                                                12450000
      ***              ***TRAN TYPE IS 'C'                              12460000
                       MOVE 'C '                  TO SWK-STMT-CRDR-ERP  12461000
                       MOVE 'ERP032:PMT Credit Adjust Txn'              12470003
                                                   TO SWK-TRANS-DES     12470103
                       COMPUTE WK-STMT-NEW-BAMT = WK-PREV-IN-BAMT       12480000
                                                + WK-DIFF-TAMT          12490000
                    END-IF                                              12500000
                    MOVE WK-STMT-NEW-BAMT  TO WK-CNV-NEW-BAMT           12510000
                    MOVE WK-CNV-NEW-BAMT(1:14) TO SWK-STMT-BAMT(1:14)   12530000
                    MOVE WK-CNV-NEW-BAMT(16:2) TO SWK-STMT-BAMT(15:2)   12540000
                    RELEASE SORT-WORK-REC                               12550000
                    MOVE WK-STMT-NEW-BAMT    TO WK-PREV-IN-BAMT         12551000
                    PERFORM 5201-MOVE-XCPTOUTF-1 THRU 5201-1-EXIT       12560000
                    PERFORM 5201-MOVE-XCPTOUTF-3 THRU 5201-3-EXIT       12570000
                    IF ACCT-STMT2-FREQ = 'D00'                          12580002
                       IF PRINT-FIRST                                   12590000
                          PERFORM 5100-PRT-HEADER   THRU 5100-EXIT      12600000
                          SET NOT-PRINT-FIRST TO TRUE                   12610000
                       END-IF                                           12620000
                       PERFORM 5600-WRITE-XCPTOUTF    THRU 5600-EXIT    12630000
                    END-IF                                              12640000
                 ELSE                                                   12650000
                    IF WK-STMT-IN-TAMT < WK-STMT-NEW-TAMT               12660000
                       SET NOT-FOUND-STMTDET  TO TRUE                   12670000
                       IF ACCT-STMT2-FREQ = 'D00'                       12680002
                          SET FOUND-XCPTOUTF     TO TRUE                12690000
                       END-IF                                           12700000
                       MOVE ZEROS             TO WK-STMT-NEW-BAMT       12710000
                       ADD 1                  TO SWK-IN-TSEQ-NO         12720000
                                                 WK-CNV-TCOUNT          12730000
                       MOVE SWK-IN-TSEQ-NO    TO SWK-STMT-SERIAL-NO     12740000
                       COMPUTE WK-DIFF-TAMT = WK-STMT-NEW-TAMT          12750000
                                            - WK-STMT-IN-TAMT           12760000
                       MOVE WK-DIFF-TAMT      TO SWK-CR-AMT             12770000
                       MOVE WK-DIFF-TAMT      TO SWK-STMT-TAMT-ERP      12780000
                       PERFORM 2900-DEFAULT-CR THRU 2900-EXIT           12790000
                       IF EWT-TRANS                                     12791055
      *>> SR-22493                                                      12792089
      *                   MOVE STMT-IN-DESC(9:12)TO SWK-CUST-REF(21:12) 12800089
                       MOVE STMT-IN-DESC-A(10:12) TO SWK-CUST-REF(21:12)12800184
      *>> SR-22493                                                      12800289
                       END-IF                                           12800383
                       IF NOT-EWT-TRANS                                 12801055
                          MOVE STMT-IN-DESC(6:12)TO SWK-CUST-REF(21:12) 12802055
                       END-IF                                           12803055
                       MOVE 'A'               TO SWK-TRANS-STATUS       12810000
                       IF SWK-STMT-CRDR = 'D '                          12820000
                          MOVE 'C '   TO    SWK-STMT-CRDR-ERP           12821000
                          MOVE 'ERP032:PMT Credit Adjust Txn'           12830003
                                                  TO SWK-TRANS-DES      12840003
                          COMPUTE WK-STMT-NEW-BAMT = WK-PREV-IN-BAMT    12850000
                                                   + WK-DIFF-TAMT       12860000
                       ELSE                                             12870000
                          MOVE 'D '   TO    SWK-STMT-CRDR-ERP           12871000
                          MOVE 'ERP031:PMT Debit Adjust Txn'            12880003
                                                 TO SWK-TRANS-DES       12890003
                          COMPUTE WK-STMT-NEW-BAMT = WK-PREV-IN-BAMT    12900000
                                                   - WK-DIFF-TAMT       12910000
                       END-IF                                           12920000
                       MOVE WK-STMT-NEW-BAMT  TO WK-CNV-NEW-BAMT        12930000
                       MOVE WK-CNV-NEW-BAMT(1:14) TO SWK-STMT-BAMT(1:14)12950000
                       MOVE WK-CNV-NEW-BAMT(16:2) TO SWK-STMT-BAMT(15:2)12960000
                       RELEASE SORT-WORK-REC                            12970000
                       MOVE WK-STMT-NEW-BAMT    TO WK-PREV-IN-BAMT      12971000
                       PERFORM 5201-MOVE-XCPTOUTF-1 THRU 5201-1-EXIT    12980000
                       PERFORM 5201-MOVE-XCPTOUTF-3 THRU 5201-3-EXIT    12990000
                       IF ACCT-STMT2-FREQ = 'D00'                       13000002
                          IF PRINT-FIRST                                13010000
                             PERFORM 5100-PRT-HEADER   THRU 5100-EXIT   13020000
                             SET NOT-PRINT-FIRST TO TRUE                13030000
                          END-IF                                        13040000
                          PERFORM 5600-WRITE-XCPTOUTF    THRU 5600-EXIT 13050000
                       END-IF                                           13060000
                    END-IF                                              13070000
                 END-IF                                                 13080000
                 PERFORM 3200-CLEAR-OCCUR-XCPT  THRU 3200-EXIT          13090000
              END-IF                                                    13100000
      *       END-IF                                                    13101000
           END-PERFORM.                                                 13110000
                                                                        13120000
       3100-EXIT. EXIT.                                                 13130000
      *-------------------------------------------------------*         13140000
       3200-CLEAR-OCCUR-XCPT.                                           13150000
                                                                        13160000
           MOVE ZEROS                  TO I.                            13170000
           MOVE SPACES                TO XCPT-P01-TRAN-SEQ.             13180000
           MOVE SPACES                TO XCPT-P01-PROD-CODE.            13190000
           MOVE SPACES                TO XCPT-P01-TDATE-YYYY.           13200000
           MOVE SPACES                TO XCPT-P01-TDATE-MM.             13210000
           MOVE SPACES                TO XCPT-P01-TDATE-DD.             13220000
           MOVE SPACES                TO XCPT-P01-VDATE-YYYY.           13230000
           MOVE SPACES                TO XCPT-P01-VDATE-MM.             13240000
           MOVE SPACES                TO XCPT-P01-VDATE-DD.             13250000
           MOVE SPACES                TO XCPT-P01-STM-CODE.             13260000
           MOVE SPACES                TO XCPT-P01-CHN-CODE.             13270000
           MOVE SPACES                TO XCPT-P01-TRAN-TYPE.            13280000
           MOVE ZEROS                 TO XCPT-P01-TRAN-AMT.             13290000
           MOVE SPACES                TO XCPT-P01-LINK-KEY.             ********
           MOVE SPACES                TO XCPT-P01-S1-BAT-REF.           ********
           PERFORM VARYING I FROM 1 BY 1 UNTIL I > TOT-CRDT             ********
              MOVE ZEROS            TO XCPT-P02-SEQ-NO(I)               ********
              MOVE SPACES           TO XCPT-P02-BEN-NAME(I)             ********
              MOVE SPACES           TO XCPT-P02-BEN-ACCT(I)             ********
              MOVE SPACES           TO XCPT-P02-BEN-BANK-CODE(I)        ********
              MOVE ZEROS            TO XCPT-P02-NET-AMT(I)              ********
              MOVE SPACES           TO XCPT-P02-CHQ-NO(I)               ********
              MOVE SPACES           TO XCPT-P02-STATUS(I)               ********
              MOVE SPACES           TO XCPT-P02-ERROR-CODE(I)           ********
              MOVE SPACES           TO XCPT-P02-CUST-REF(I)             ********
           END-PERFORM.                                                 ********
                                                                        ********
       3200-EXIT. EXIT.                                                 ********
      *-------------------------------------------------------*         ********
       3300-GEN-STMT-DETAIL.                                            ********
                                                                        ********
           SET FOUND-STMTDET       TO TRUE.                             ********
           SET NOT-EOF-STMTDET     TO TRUE.                             13490000
           MOVE LOW-VALUE          TO DRBT-CNTL-REC.                    13491058
      *    MOVE LOW-VALUE          TO DRBT-KEY-COMP-ID                  13500058
      *                               DRBT-KEY-CN-REF                   13510058
      *                               DRBT-KEY-CR-SEQ.                  13520058
           MOVE LOW-VALUE          TO CRDT-CNTL-REC.                    13530058
      *    MOVE LOW-VALUE          TO CRDT-KEY-COMP-ID                  13531058
      *                               CRDT-KEY-CN-REF                   13540058
      *                               CRDT-KEY-CR-SEQ.                  13550058
                                                                        13551058
           IF STMT-IN-CRDR = 'D ' THEN                                  13560000
      *       *** CASE - DEBIT TRANSACTION ***                          13570000
             IF EWT-TRANS                                               13571054
              MOVE SPACES              TO DRBT-CNTL-REC                 13580074
      *>> SR-22493                                                      13580189
      *       MOVE STMT-IN-DESC(9:12)  TO DRBT-KEY-CUST-REF             13581089
              MOVE STMT-IN-DESC-A(10:12)  TO DRBT-KEY-CUST-REF          13582084
              MOVE STMT-KEY-IN-ACCT-NO TO DRBT-KEY-DRBT-ACCT            13590000
      *       MOVE STMT-IN-DESC(1:3)   TO DRBT-KEY-PROD-CODE            13600089
      *       MOVE STMT-IN-DESC(5:3)   TO DRBT-KEY-PROD-CODE2           13601089
              MOVE STMT-IN-DESC(2:3)   TO DRBT-KEY-PROD-CODE            13601183
              MOVE STMT-IN-DESC(6:3)   TO DRBT-KEY-PROD-CODE2           13601283
      *<< SR-22493                                                      13601389
      **      DISPLAY 'EWT : DRBT-CNTL-REC =>' DRBT-CNTL-REC            13602078
      **      DISPLAY 'STMT-IN-DESC(5:3)    :' STMT-IN-DESC(5:3)        13603078
              PERFORM 2600-START-DRBTIOF THRU 2600-EXIT                 13610000
             END-IF                                                     13611054
             IF NOT-EWT-TRANS                                           13612054
              MOVE SPACES              TO DRBT-CNTL-REC                 13612176
              MOVE STMT-IN-DESC(6:12)  TO DRBT-KEY-CUST-REF             13613054
              MOVE STMT-KEY-IN-ACCT-NO TO DRBT-KEY-DRBT-ACCT            13614054
              MOVE STMT-IN-DESC(2:3)   TO DRBT-KEY-PROD-CODE            13615054
              PERFORM 2600-START-DRBTIOF THRU 2600-EXIT                 13616054
             END-IF                                                     13617054
           ELSE                                                         13620000
      *       *** CASE - CREDIT TRANSACTION ***                         13630000
             IF EWT-TRANS                                               13631054
              MOVE SPACES              TO CRDT-CNTL-REC                 13632075
      *>> SR-22493                                                      13633089
      *       MOVE STMT-IN-DESC(9:12)  TO CRDT-KEY-CUST-REF             13640089
              MOVE STMT-IN-DESC-A(10:12) TO CRDT-KEY-CUST-REF           13641084
              MOVE STMT-KEY-IN-ACCT-NO TO CRDT-KEY-DRBT-ACCT            13650000
      *       MOVE STMT-IN-DESC(1:3)   TO CRDT-KEY-PROD-CODE            13660089
      *       MOVE STMT-IN-DESC(5:3)   TO CRDT-KEY-PROD-CODE2           13661089
              MOVE STMT-IN-DESC(2:3)   TO CRDT-KEY-PROD-CODE            13662083
              MOVE STMT-IN-DESC(6:3)   TO CRDT-KEY-PROD-CODE2           13663083
      *<< SR-22493                                                      13664089
              PERFORM 2700-START-CRDTIOF THRU 2700-EXIT                 13670000
             END-IF                                                     13670154
             IF NOT-EWT-TRANS                                           13670254
              MOVE SPACES              TO CRDT-CNTL-REC                 13670376
              MOVE STMT-IN-DESC(6:12)  TO CRDT-KEY-CUST-REF             13671054
              MOVE STMT-KEY-IN-ACCT-NO TO CRDT-KEY-DRBT-ACCT            13672054
              MOVE STMT-IN-DESC(2:3)   TO CRDT-KEY-PROD-CODE            13673054
              PERFORM 2700-START-CRDTIOF THRU 2700-EXIT                 13674054
             END-IF                                                     13675054
           END-IF.                                                      13680000
           IF EWT-TRANS                                                 13690055
              PERFORM 3310-GEN-STMT-DETAIL-EWT-TRANS THRU 3310-EXIT     13740055
           END-IF.                                                      13750055
           IF NOT-EWT-TRANS                                             13760055
              PERFORM 3320-GEN-STMT-DETAIL-NOT-EWT THRU 3320-EXIT       13761056
           END-IF.                                                      13780055
                                                                        14620000
       3300-EXIT. EXIT.                                                 14630000
      *-------------------------------------------------------*         14640055
       3310-GEN-STMT-DETAIL-EWT-TRANS.                                  14641555
           IF FOUND-STMTDET                                             14641655
              IF STMT-IN-CRDR = 'D ' THEN                               14641755
      *          *** CASE - DEBIT TRANSACTION ***                       14641855
                 PERFORM 2400-READ-DRBTIOF-RTN THRU 2400-EXIT           14641955
      *>> SR-22493                                                      14642089
      *          IF DRBT-KEY-CUST-REF  NOT = STMT-IN-DESC(9:12)         14642189
                 IF DRBT-KEY-CUST-REF  NOT = STMT-IN-DESC-A(10:12)      14642284
                    OR                                                  14642383
                    DRBT-KEY-DRBT-ACCT NOT = STMT-KEY-IN-ACCT-NO        14642483
                    OR                                                  14642583
      *             DRBT-KEY-PROD-CODE  NOT = STMT-IN-DESC(1:3)         14642689
                    DRBT-KEY-PROD-CODE  NOT = STMT-IN-DESC(2:3)         14642783
                    OR                                                  14642883
      *             DRBT-KEY-PROD-CODE2 NOT = STMT-IN-DESC(5:3)         14642989
                    DRBT-KEY-PROD-CODE2 NOT = STMT-IN-DESC(6:3)         14643083
      *<< SR-22493                                                      14643189
                    OR EOF-STMTDET                                      14643283
     ***         DISPLAY 'DRBT-KEY-CUST-REF   :'DRBT-KEY-CUST-REF       14643383
     ***         DISPLAY 'DRBT-KEY-DRBT-ACCT  :'DRBT-KEY-DRBT-ACCT      14643483
     ***         DISPLAY 'DRBT-KEY-PROD-CODE  :'DRBT-KEY-PROD-CODE      14643583
     ***         DISPLAY 'DRBT-KEY-PROD-CODE2 :'DRBT-KEY-PROD-CODE2     14643683
     ***         DISPLAY 'STMT-IN-DESC-A(10:12) :'STMT-IN-DESC-A(10:12) 14643890
     ***         DISPLAY 'STMT-KEY-IN-ACCT-NO :'STMT-KEY-IN-ACCT-NO     14643990
     ***         DISPLAY 'STMT-IN-DESC(1:3)   :'STMT-IN-DESC(1:3)       14644090
     ***         DISPLAY 'STMT-IN-DESC(5:3)   :'STMT-IN-DESC(5:3)       14644190
     ***         DISPLAY 'SW-STMTDET-EOF      :'SW-STMTDET-EOF          14644290
                    SET NOT-FOUND-STMTDET   TO TRUE                     14644390
                    IF ACCT-STMT2-FREQ = 'D00'                          14644490
                       SET FOUND-XCPTOUTF     TO TRUE                   14644590
                    END-IF                                              14644690
                    ADD 1                   TO SWK-IN-TSEQ-NO           14644790
                                               WK-CNV-TCOUNT            14644890
                    MOVE SWK-IN-TSEQ-NO     TO SWK-STMT-SERIAL-NO       14644990
                    MOVE 'A'                TO SWK-TRANS-STATUS         14645090
                   MOVE 'ERP033:PMT EWT Debit Adjust-Not Found Data Key'14645190
                                           TO P02-ERROR-CODE            14645290
                                               SWK-TRANS-DES            14645390
      *>> SR-22493                                                      14645490
      *             MOVE STMT-IN-DESC(9:12)                             14645590
      *                                TO SWK-CUST-REF(21:12)           14645690
                    MOVE STMT-IN-DESC-A(10:12)                          14645790
                                       TO SWK-CUST-REF(21:12)           14645890
      *<< SR-22493                                                      14645990
                    PERFORM 4200-SUM-PREV-BAMT THRU 4200-EXIT           14646090
                    RELEASE SORT-WORK-REC                               14646190
                    PERFORM 5200-MOVE-XCPTOUTF-1 THRU 5200-1-EXIT       14646290
                    PERFORM 5200-MOVE-XCPTOUTF-2 THRU 5200-2-EXIT       14646390
                    IF PRINT-FIRST                                      14646490
                       PERFORM 5100-PRT-HEADER   THRU 5100-EXIT         14646590
                       SET NOT-PRINT-FIRST       TO TRUE                14646690
                    END-IF                                              14646790
                    PERFORM 5300-WRITE-XCPTOUTF  THRU 5300-EXIT         14646890
                 ELSE                                                   14646990
                    PERFORM 3400-DR-CHK-GEN-STMT-DETAIL THRU 3400-EXIT  14647090
                    IF  N-DR-STMTDET-RTN                                14647190
                        ADD 1                  TO SWK-IN-TSEQ-NO        14647290
                                               WK-CNV-TCOUNT            14647390
                        MOVE SWK-IN-TSEQ-NO    TO SWK-STMT-SERIAL-NO    14647490
                        PERFORM 4200-SUM-PREV-BAMT THRU 4200-EXIT       14647590
                        RELEASE SORT-WORK-REC                           14647690
                    ELSE                                                14647790
                        PERFORM 3100-STMTDET-RTN THRU 3100-EXIT         14647890
                    END-IF                                              14647990
                 END-IF                                                 14648090
              ELSE                                                      14648190
      *          *** CASE - CREDIT TRANSACTION ***                      14648290
                 PERFORM 2500-READ-CRDTIOF-RTN THRU 2500-EXIT           14648390
      *>> SR-22493                                                      14648490
      *          IF CRDT-KEY-CUST-REF NOT = STMT-IN-DESC(9:12)          14648590
                 IF CRDT-KEY-CUST-REF NOT = STMT-IN-DESC-A(10:12)       14648690
                    OR                                                  14648790
                    CRDT-KEY-DRBT-ACCT NOT = STMT-KEY-IN-ACCT-NO        14648890
                    OR                                                  14648990
      *             CRDT-KEY-PROD-CODE  NOT = STMT-IN-DESC(1:3)         14649090
                    CRDT-KEY-PROD-CODE  NOT = STMT-IN-DESC(2:3)         14649190
                    OR                                                  14649290
      *             CRDT-KEY-PROD-CODE2 NOT = STMT-IN-DESC(5:3)         14649390
                    CRDT-KEY-PROD-CODE2 NOT = STMT-IN-DESC(6:3)         14649490
      *<< SR-22493                                                      14649590
                    OR EOF-STMTDET                                      14649690
                    ADD 1                   TO SWK-IN-TSEQ-NO           14649790
                                               WK-CNV-TCOUNT            14649890
                    MOVE SWK-IN-TSEQ-NO     TO SWK-STMT-SERIAL-NO       14649990
                    PERFORM 4200-SUM-PREV-BAMT THRU 4200-EXIT           14650090
                    RELEASE SORT-WORK-REC                               14650190
                 ELSE                                                   14650290
                    PERFORM 3450-CR-CHK-GEN-STMT-DETAIL THRU 3450-EXIT  14650390
                    IF  N-CR-STMTDET-RTN                                14650490
                        ADD 1                  TO SWK-IN-TSEQ-NO        14650590
                                               WK-CNV-TCOUNT            14650690
                        MOVE SWK-IN-TSEQ-NO    TO SWK-STMT-SERIAL-NO    14650790
                        PERFORM 4200-SUM-PREV-BAMT THRU 4200-EXIT       14650890
                        RELEASE SORT-WORK-REC                           14650990
                    ELSE                                                14651090
                        PERFORM 3100-STMTDET-RTN THRU 3100-EXIT         14651190
                    END-IF                                              14651290
                 END-IF                                                 14651390
              END-IF                                                    14651490
           ELSE                                                         14651590
              ADD 1                   TO SWK-IN-TSEQ-NO                 14651690
                                         WK-CNV-TCOUNT                  14651790
              MOVE SWK-IN-TSEQ-NO     TO SWK-STMT-SERIAL-NO             14651890
              IF STMT-IN-CRDR = 'D ' THEN                               14651990
      *          *** CASE - DEBIT TRANSACTION ***                       14652090
                 MOVE 'A'              TO SWK-TRANS-STATUS              14652190
                MOVE 'ERP033:PMT EWT Debit Adjust-Not Found Data Key'   14652290
                                        TO P02-ERROR-CODE               14652390
                                           SWK-TRANS-DES                14652490
      *>> SR-22493                                                      14652590
      *          MOVE STMT-IN-DESC(9:12)                                14652690
      *                             TO SWK-CUST-REF(21:12)              14652790
                 MOVE STMT-IN-DESC-A(10:12)                             14652890
                                    TO SWK-CUST-REF(21:12)              14652990
      *<< SR-22493                                                      14653090
                 PERFORM 4200-SUM-PREV-BAMT THRU 4200-EXIT              14653190
                 RELEASE SORT-WORK-REC                                  14653290
                 PERFORM 5200-MOVE-XCPTOUTF-1 THRU 5200-1-EXIT          14653390
                 PERFORM 5200-MOVE-XCPTOUTF-2 THRU 5200-2-EXIT          14653490
                 IF ACCT-STMT2-FREQ = 'D00'                             14653590
                    IF PRINT-FIRST                                      14653690
                       PERFORM 5100-PRT-HEADER   THRU 5100-EXIT         14653790
                       SET NOT-PRINT-FIRST       TO TRUE                14653890
                    END-IF                                              14653990
                    PERFORM 5300-WRITE-XCPTOUTF  THRU 5300-EXIT         14654090
                 END-IF                                                 14654190
              ELSE                                                      14654290
                 PERFORM 4200-SUM-PREV-BAMT THRU 4200-EXIT              14654390
                 RELEASE SORT-WORK-REC                                  14654490
              END-IF                                                    14654590
           END-IF.                                                      14654690
                                                                        14654790
       3310-EXIT. EXIT.                                                 14654890
      *-------------------------------------------------------*         14654990
       3320-GEN-STMT-DETAIL-NOT-EWT.                                    14655090
           IF FOUND-STMTDET                                             14655190
              IF STMT-IN-CRDR = 'D ' THEN                               14655290
      *          *** CASE - DEBIT TRANSACTION ***                       14655390
                 PERFORM 2400-READ-DRBTIOF-RTN THRU 2400-EXIT           14655490
                 IF DRBT-KEY-CUST-REF  NOT = STMT-IN-DESC(6:12)         14655590
                    OR                                                  14655690
                    DRBT-KEY-DRBT-ACCT NOT = STMT-KEY-IN-ACCT-NO        14655790
                    OR                                                  14655890
                    DRBT-KEY-PROD-CODE NOT = STMT-IN-DESC(2:3)          14655990
                    OR EOF-STMTDET                                      14656090
                    SET NOT-FOUND-STMTDET   TO TRUE                     14656190
                    IF ACCT-STMT2-FREQ = 'D00'                          14656290
                       SET FOUND-XCPTOUTF     TO TRUE                   14656390
                    END-IF                                              14656490
                    ADD 1                   TO SWK-IN-TSEQ-NO           14656590
                                               WK-CNV-TCOUNT            14656690
                    MOVE SWK-IN-TSEQ-NO     TO SWK-STMT-SERIAL-NO       14656790
                    MOVE 'A'                TO SWK-TRANS-STATUS         14656890
                    MOVE 'ERP033:PMT Debit Adjust-Not Found Data Key'   14656990
                                           TO P02-ERROR-CODE            14657090
                                               SWK-TRANS-DES            14657190
                    MOVE STMT-IN-DESC(6:12)                             14657290
                                       TO SWK-CUST-REF(21:12)           14657390
                    PERFORM 4200-SUM-PREV-BAMT THRU 4200-EXIT           14657490
                    RELEASE SORT-WORK-REC                               14657590
                    PERFORM 5200-MOVE-XCPTOUTF-1 THRU 5200-1-EXIT       14657690
                    PERFORM 5200-MOVE-XCPTOUTF-2 THRU 5200-2-EXIT       14657790
                    IF PRINT-FIRST                                      14657890
                       PERFORM 5100-PRT-HEADER   THRU 5100-EXIT         14657990
                       SET NOT-PRINT-FIRST       TO TRUE                14658090
                    END-IF                                              14658190
                    PERFORM 5300-WRITE-XCPTOUTF  THRU 5300-EXIT         14658290
                 ELSE                                                   14658390
                    PERFORM 3400-DR-CHK-GEN-STMT-DETAIL THRU 3400-EXIT  14658490
                    IF  N-DR-STMTDET-RTN                                14658590
                        ADD 1                  TO SWK-IN-TSEQ-NO        14658690
                                               WK-CNV-TCOUNT            14658790
                        MOVE SWK-IN-TSEQ-NO    TO SWK-STMT-SERIAL-NO    14658890
                        PERFORM 4200-SUM-PREV-BAMT THRU 4200-EXIT       14658990
                        RELEASE SORT-WORK-REC                           14659090
                    ELSE                                                14659190
                        PERFORM 3100-STMTDET-RTN THRU 3100-EXIT         14659290
                    END-IF                                              14659390
                 END-IF                                                 14659490
              ELSE                                                      14659590
      *          *** CASE - CREDIT TRANSACTION ***                      14659690
                 PERFORM 2500-READ-CRDTIOF-RTN THRU 2500-EXIT           14659790
                 IF CRDT-KEY-CUST-REF NOT = STMT-IN-DESC(6:12)          14659890
                    OR                                                  14659990
                    CRDT-KEY-DRBT-ACCT NOT = STMT-KEY-IN-ACCT-NO        14660090
                    OR                                                  14660190
                    CRDT-KEY-PROD-CODE NOT = STMT-IN-DESC(2:3)          14660290
                    OR EOF-STMTDET                                      14660390
                    ADD 1                   TO SWK-IN-TSEQ-NO           14660490
                                               WK-CNV-TCOUNT            14660590
                    MOVE SWK-IN-TSEQ-NO     TO SWK-STMT-SERIAL-NO       14660690
                    PERFORM 4200-SUM-PREV-BAMT THRU 4200-EXIT           14660790
                    RELEASE SORT-WORK-REC                               14660890
                 ELSE                                                   14660990
                    PERFORM 3450-CR-CHK-GEN-STMT-DETAIL THRU 3450-EXIT  14661090
                    IF  N-CR-STMTDET-RTN                                14661190
                        ADD 1                  TO SWK-IN-TSEQ-NO        14661290
                                               WK-CNV-TCOUNT            14661390
                        MOVE SWK-IN-TSEQ-NO    TO SWK-STMT-SERIAL-NO    14661490
                        PERFORM 4200-SUM-PREV-BAMT THRU 4200-EXIT       14661590
                        RELEASE SORT-WORK-REC                           14661690
                    ELSE                                                14661790
                        PERFORM 3100-STMTDET-RTN THRU 3100-EXIT         14661890
                    END-IF                                              14661990
                 END-IF                                                 14662090
              END-IF                                                    14662190
           ELSE                                                         14662290
              ADD 1                   TO SWK-IN-TSEQ-NO                 14662390
                                         WK-CNV-TCOUNT                  14662490
              MOVE SWK-IN-TSEQ-NO     TO SWK-STMT-SERIAL-NO             14662590
              IF STMT-IN-CRDR = 'D ' THEN                               14662690
      *          *** CASE - DEBIT TRANSACTION ***                       14662790
                 MOVE 'A'              TO SWK-TRANS-STATUS              14662890
                 MOVE 'ERP033:PMT Debit Adjust-Not Found Data Key'      14662990
                                        TO P02-ERROR-CODE               14663090
                                           SWK-TRANS-DES                14663190
                 MOVE STMT-IN-DESC(6:12)                                14663255
                                    TO SWK-CUST-REF(21:12)              14663355
                 PERFORM 4200-SUM-PREV-BAMT THRU 4200-EXIT              14663455
                 RELEASE SORT-WORK-REC                                  14663555
                 PERFORM 5200-MOVE-XCPTOUTF-1 THRU 5200-1-EXIT          14663655
                 PERFORM 5200-MOVE-XCPTOUTF-2 THRU 5200-2-EXIT          14663755
                 IF ACCT-STMT2-FREQ = 'D00'                             14663855
                    IF PRINT-FIRST                                      14663955
                       PERFORM 5100-PRT-HEADER   THRU 5100-EXIT         14664055
                       SET NOT-PRINT-FIRST       TO TRUE                14664155
                    END-IF                                              14664255
                    PERFORM 5300-WRITE-XCPTOUTF  THRU 5300-EXIT         14664355
                 END-IF                                                 14664455
              ELSE                                                      14664555
                 PERFORM 4200-SUM-PREV-BAMT THRU 4200-EXIT              14664655
                 RELEASE SORT-WORK-REC                                  14664755
              END-IF                                                    14664855
           END-IF.                                                      14664955
       3320-EXIT. EXIT.                                                 14665055
      *-------------------------------------------------------*         14665155
       3400-DR-CHK-GEN-STMT-DETAIL.                                     14665255
                                                                        14665355
           SET Y-DR-STMTDET-RTN  TO TRUE.                               14665455
           IF  (STMT-IN-DESC(2:3)  = 'MCL' AND                          14665555
               (DRBT-DR-AC-TYPE = '94' OR '95' OR '96'))                14665655
               SET N-DR-STMTDET-RTN  TO TRUE.                           14665755
                                                                        14665855
       3400-EXIT. EXIT.                                                 14665955
      *-------------------------------------------------------*         14666055
       3450-CR-CHK-GEN-STMT-DETAIL.                                     14666155
                                                                        14666255
           SET Y-CR-STMTDET-RTN  TO TRUE.                               14666355
           IF  (STMT-IN-DESC(2:3)  = 'MCL' AND                          14666455
               (CRDT-DR-AC-TYPE = '94' OR '95' OR '96'))                14666555
               SET N-CR-STMTDET-RTN  TO TRUE.                           14666655
                                                                        14666755
       3450-EXIT. EXIT.                                                 14666855
      *-------------------------------------------------------*         14666955
       3500-BNT-CHECK-PROCESS-RECORD.                                   14667055
           IF STMT-IN-CRDR = 'D '                                       14667155
              COMPUTE WK-CN-TRANS-AMT = FUNCTION NUMVAL(DRBT-CR-AMT)    14667280
      *       COMPUTE WK-CN-TRANS-AMT = FUNCTION NUMVAL(DRBT-CR-NET-AMT)14667380
              IF WK-STMT-IN-TAMT NOT = WK-CN-TRANS-AMT                  14667479
                 PERFORM 2400-READ-DRBTIOF-RTN THRU 2400-EXIT           14667579
                 IF NOT-EOF-STMTDET                                     14667679
                    IF DRBT-KEY-CUST-REF NOT = STMT-IN-DESC(6:12)       14667779
                       OR DRBT-KEY-DRBT-ACCT NOT = STMT-KEY-IN-ACCT-NO  14667879
                       OR DRBT-KEY-PROD-CODE NOT = STMT-IN-DESC(2:3)    14667979
                       SET CHANGE-STMTDET TO TRUE                       14668079
                    END-IF                                              14668179
                    GO TO 3500-BNT-CHECK-PROCESS-RECORD                 14668279
                 END-IF                                                 14668379
              ELSE                                                      14668479
                 IF DRBT-BNT-PROCESS = 'Y'                              14668579
                    PERFORM 2400-READ-DRBTIOF-RTN THRU 2400-EXIT        14668679
                    IF NOT-EOF-STMTDET                                  14668779
                       IF DRBT-KEY-CUST-REF NOT = STMT-IN-DESC(6:12)    14668879
                         OR DRBT-KEY-DRBT-ACCT NOT = STMT-KEY-IN-ACCT-NO14668979
                         OR DRBT-KEY-PROD-CODE NOT = STMT-IN-DESC(2:3)  14669079
                         SET CHANGE-STMTDET TO TRUE                     14669179
                       END-IF                                           14669279
                    GO TO 3500-BNT-CHECK-PROCESS-RECORD                 14669379
                    END-IF                                              14669479
                 END-IF                                                 14669579
              END-IF                                                    14669679
              IF WK-STMT-IN-TAMT = WK-CN-TRANS-AMT                      14669779
                 AND DRBT-KEY-CUST-REF = STMT-IN-DESC(6:12)             14669879
                 AND DRBT-BNT-PROCESS  NOT = 'Y'                        14669979
                 MOVE 'Y'       TO   DRBT-BNT-PROCESS                   14670079
                 REWRITE DRBT-IO-REC                                    14670179
                 SET CHANGE-STMTDET TO TRUE                             14670279
                 SET Y-BNT-DET-FND  TO TRUE                             14670379
              END-IF                                                    14670479
           ELSE                                                         14670579
    ***       COMPUTE WK-CN-TRANS-AMT = FUNCTION NUMVAL(CRDT-CR-AMT)    14670679
              COMPUTE WK-CN-TRANS-AMT = FUNCTION NUMVAL(CRDT-CR-NET-AMT)14670779
              IF WK-STMT-IN-TAMT NOT = WK-CN-TRANS-AMT                  14670879
                 PERFORM 2500-READ-CRDTIOF-RTN THRU 2500-EXIT           14670979
                 IF NOT-EOF-STMTDET                                     14671079
                   IF (CRDT-KEY-CUST-REF NOT = STMT-IN-DESC(6:12))      14671179
                      OR CRDT-KEY-DRBT-ACCT NOT = STMT-KEY-IN-ACCT-NO   14671279
                      OR CRDT-KEY-PROD-CODE NOT = STMT-IN-DESC(2:3)     14671379
                      SET CHANGE-STMTDET TO TRUE                        14671479
                   END-IF                                               14671579
                   GO TO 3500-BNT-CHECK-PROCESS-RECORD                  14671679
                 END-IF                                                 14671779
              ELSE                                                      14671879
                 IF CRDT-BNT-PROCESS = 'Y'                              14671979
                    PERFORM 2500-READ-CRDTIOF-RTN THRU 2500-EXIT        14672079
                    IF NOT-EOF-STMTDET                                  14672179
                      IF (CRDT-KEY-CUST-REF NOT = STMT-IN-DESC(6:12))   14672279
                         OR CRDT-KEY-DRBT-ACCT NOT = STMT-KEY-IN-ACCT-NO14672379
                         OR CRDT-KEY-PROD-CODE NOT = STMT-IN-DESC(2:3)  14672479
                         SET CHANGE-STMTDET TO TRUE                     14672579
                       END-IF                                           14672679
                      GO TO 3500-BNT-CHECK-PROCESS-RECORD               14672779
                    END-IF                                              14672879
                 END-IF                                                 14672979
              END-IF                                                    14673079
              IF WK-STMT-IN-TAMT = WK-CN-TRANS-AMT                      14673179
                 AND CRDT-KEY-CUST-REF = STMT-IN-DESC(6:12)             14673279
                 AND CRDT-BNT-PROCESS NOT = 'Y'                         14673379
                 MOVE 'Y'       TO   CRDT-BNT-PROCESS                   14673479
                 REWRITE CRDT-IO-REC                                    14673579
                 SET CHANGE-STMTDET TO TRUE                             14673679
                 SET Y-BNT-DET-FND  TO TRUE                             14673779
              END-IF                                                    14673879
           END-IF.                                                      14673979
                                                                        14674079
       3500-EXIT. EXIT.                                                 14674179
      *-------------------------------------------------------*         14674279
       3700-EWT-BNT-CHECK-PROCESS-REC.                                  14674379
           IF STMT-IN-CRDR = 'D '                                       14674479
     ***      DISPLAY 'NUMVAL(DRBT-CR-AMT) => 1 ' DRBT-CR-AMT           14674579
              COMPUTE WK-CN-TRANS-AMT = FUNCTION NUMVAL(DRBT-CR-AMT)    14674680
     ***      COMPUTE WK-CN-TRANS-AMT = FUNCTION NUMVAL(DRBT-CR-NET-AMT)14674780
              IF WK-STMT-IN-TAMT NOT = WK-CN-TRANS-AMT                  14674879
                 PERFORM 2400-READ-DRBTIOF-RTN THRU 2400-EXIT           14674979
                 IF NOT-EOF-STMTDET                                     14675079
      *>> SR-22493                                                      14675189
      *             IF DRBT-KEY-CUST-REF NOT = STMT-IN-DESC(9:12)       14675289
                    IF DRBT-KEY-CUST-REF NOT = STMT-IN-DESC-A(10:12)    14675384
                       OR DRBT-KEY-DRBT-ACCT NOT = STMT-KEY-IN-ACCT-NO  14675483
      *                OR DRBT-KEY-PROD-CODE NOT = STMT-IN-DESC(1:3)    14675589
      *                OR DRBT-KEY-PROD-CODE2 NOT = STMT-IN-DESC(5:3)   14675689
                       OR DRBT-KEY-PROD-CODE NOT = STMT-IN-DESC(2:3)    14675783
                       OR DRBT-KEY-PROD-CODE2 NOT = STMT-IN-DESC(6:3)   14675883
      *<< SR-22493                                                      14675989
                       SET CHANGE-STMTDET TO TRUE                       14676083
                    END-IF                                              14676183
                    GO TO 3700-EWT-BNT-CHECK-PROCESS-REC                14676283
                 END-IF                                                 14676383
              ELSE                                                      14676483
                 IF DRBT-BNT-PROCESS = 'Y'                              14676583
                    PERFORM 2400-READ-DRBTIOF-RTN THRU 2400-EXIT        14676683
                    IF NOT-EOF-STMTDET                                  14676783
      *>> SR-22493                                                      14676889
      *                IF DRBT-KEY-CUST-REF NOT = STMT-IN-DESC(9:12)    14676989
                       IF DRBT-KEY-CUST-REF NOT = STMT-IN-DESC-A(10:12) 14677084
                         OR DRBT-KEY-DRBT-ACCT NOT = STMT-KEY-IN-ACCT-NO14677183
      *                  OR DRBT-KEY-PROD-CODE NOT = STMT-IN-DESC(1:3)  14677289
      *                  OR DRBT-KEY-PROD-CODE2 NOT = STMT-IN-DESC(5:3) 14677389
                         OR DRBT-KEY-PROD-CODE NOT = STMT-IN-DESC(2:3)  14677483
                         OR DRBT-KEY-PROD-CODE2 NOT = STMT-IN-DESC(6:3) 14677583
      *<< SR-22493                                                      14677689
                         SET CHANGE-STMTDET TO TRUE                     14677783
                       END-IF                                           14677883
                    GO TO 3700-EWT-BNT-CHECK-PROCESS-REC                14677983
                    END-IF                                              14678083
                 END-IF                                                 14678183
              END-IF                                                    14678283
              IF WK-STMT-IN-TAMT = WK-CN-TRANS-AMT                      14678383
      *>> SR-22493                                                      14678492
      *          AND DRBT-KEY-CUST-REF = STMT-IN-DESC(9:12)             14678592
                 AND DRBT-KEY-CUST-REF = STMT-IN-DESC-A(10:12)          14678684
      *<< SR-22493                                                      14678792
                 AND DRBT-BNT-PROCESS  NOT = 'Y'                        14678883
                 MOVE 'Y'       TO   DRBT-BNT-PROCESS                   14678983
                 REWRITE DRBT-IO-REC                                    14679083
                 SET CHANGE-STMTDET TO TRUE                             14679183
                 SET Y-BNT-DET-FND  TO TRUE                             14679283
              END-IF                                                    14679383
           ELSE                                                         14679483
     ***      DISPLAY 'NUMVAL(CRDT-CR-AMT) => 2 ' CRDT-CR-AMT           14679583
     ***      COMPUTE WK-CN-TRANS-AMT = FUNCTION NUMVAL(CRDT-CR-AMT)    14679683
              COMPUTE WK-CN-TRANS-AMT = FUNCTION NUMVAL(CRDT-CR-NET-AMT)14679783
              IF WK-STMT-IN-TAMT NOT = WK-CN-TRANS-AMT                  14679883
                 PERFORM 2500-READ-CRDTIOF-RTN THRU 2500-EXIT           14679983
                 IF NOT-EOF-STMTDET                                     14680083
      *>> SR-22493                                                      14680189
      *            IF (CRDT-KEY-CUST-REF NOT = STMT-IN-DESC(9:12))      14680289
                   IF (CRDT-KEY-CUST-REF NOT = STMT-IN-DESC-A(10:12))   14680384
                      OR CRDT-KEY-DRBT-ACCT  NOT = STMT-KEY-IN-ACCT-NO  14680483
      *               OR CRDT-KEY-PROD-CODE  NOT = STMT-IN-DESC(1:3)    14680589
      *               OR CRDT-KEY-PROD-CODE2 NOT = STMT-IN-DESC(5:3)    14680689
                      OR CRDT-KEY-PROD-CODE  NOT = STMT-IN-DESC(2:3)    14680783
                      OR CRDT-KEY-PROD-CODE2 NOT = STMT-IN-DESC(6:3)    14680883
      *<< SR-22493                                                      14680989
                      SET CHANGE-STMTDET TO TRUE                        14681083
                   END-IF                                               14681183
                   GO TO 3700-EWT-BNT-CHECK-PROCESS-REC                 14681283
                 END-IF                                                 14681383
              ELSE                                                      14681483
                 IF CRDT-BNT-PROCESS = 'Y'                              14681583
                    PERFORM 2500-READ-CRDTIOF-RTN THRU 2500-EXIT        14681683
                    IF NOT-EOF-STMTDET                                  14681783
      *>> SR-22493                                                      14681889
      *               IF (CRDT-KEY-CUST-REF NOT = STMT-IN-DESC(9:12))   14681989
                      IF (CRDT-KEY-CUST-REF NOT = STMT-IN-DESC-A(10:12))14682084
                         OR CRDT-KEY-DRBT-ACCT NOT = STMT-KEY-IN-ACCT-NO14682183
      *                  OR CRDT-KEY-PROD-CODE  NOT = STMT-IN-DESC(1:3) 14682289
      *                  OR CRDT-KEY-PROD-CODE2 NOT = STMT-IN-DESC(5:3) 14682389
                         OR CRDT-KEY-PROD-CODE  NOT = STMT-IN-DESC(2:3) 14682483
                         OR CRDT-KEY-PROD-CODE2 NOT = STMT-IN-DESC(6:3) 14682583
      *<< SR-22493                                                      14682689
                         SET CHANGE-STMTDET TO TRUE                     14682783
                       END-IF                                           14682883
                      GO TO 3700-EWT-BNT-CHECK-PROCESS-REC              14682983
                    END-IF                                              14683083
                 END-IF                                                 14683183
              END-IF                                                    14683283
              IF WK-STMT-IN-TAMT = WK-CN-TRANS-AMT                      14683383
      *>> SR-22493                                                      14683489
      *          AND CRDT-KEY-CUST-REF = STMT-IN-DESC(9:12)             14683589
                 AND CRDT-KEY-CUST-REF = STMT-IN-DESC-A(10:12)          14683684
      *<< SR-22493                                                      14683789
                 AND CRDT-BNT-PROCESS NOT = 'Y'                         14683883
                 MOVE 'Y'       TO   CRDT-BNT-PROCESS                   14683983
                 REWRITE CRDT-IO-REC                                    14684083
                 SET CHANGE-STMTDET TO TRUE                             14684183
                 SET Y-BNT-DET-FND  TO TRUE                             14684283
              END-IF                                                    14684383
           END-IF.                                                      14684483
                                                                        14684583
       3700-EXIT. EXIT.                                                 14684683
      *-------------------------------------------------------*         14684783
       4000-SORT-OUTPUT-RTN.                                            14684883
                                                                        14684983
           IF NOT-FOUND-STMTINF                                         14685083
              GO TO 4000-EXIT                                           14686083
           END-IF.                                                      14690000
                                                                        14700000
           RETURN SORT-WORK RECORD                                      14710000
                  AT END                                                14720000
                  GO TO 4000-EXIT.                                      14740000
           WRITE STMT-OUTD-REC FROM SWK-DETL-REC.                       14750000
           GO TO 4000-SORT-OUTPUT-RTN.                                  14760000
                                                                        14770000
       4000-EXIT. EXIT.                                                 14780000
      *-------------------------------------------------------*         14790000
      *4100-SEARCH-BRANCH.                                              14800000
      *                                                                 14810000
      *    MOVE STMT-IN-BRNO TO BRAN-CNTL-REC.                          14820000
      *    READ BRAN-IN-FL KEY IS BRAN-CNTL-REC                         14830000
      *         INVALID KEY MOVE SPACE TO SWK-BR-NAME                   14840000
      *                     GO TO 4100-EXIT.                            14850000
      *    MOVE BR-NAME TO SWK-BR-NAME.                                 14860000
      *                                                                 14870000
      *4100-EXIT. EXIT.                                                 14880000
      *-------------------------------------------------------*         14890000
       4200-SUM-PREV-BAMT.                                              14951000
            IF STMT-IN-BAMT-SIGN = 'D'                                  14951100
               COMPUTE WK-PREV-IN-BAMT = WK-STMT-IN-BAMT * -1           14951200
            ELSE                                                        14951300
               COMPUTE WK-PREV-IN-BAMT = WK-STMT-IN-BAMT *  1           14951400
            END-IF.                                                     14951500
       4200-EXIT. EXIT.                                                 14955000
      *-------------------------------------------------------*         14956000
       5000-RMP-INIT-RTN.                                               14960000
            MOVE 1                    TO SERVICE-REQUEST-CODE.          14970000
            MOVE 1                    TO INT-TOTAL-REPORT.              14980000
            SET REPORT-INDEX          TO 1.                             14990000
                                                                        15000000
            MOVE 'ERP '               TO WK-CHANNEL-KEY.                15010000
            SEARCH REPORT-NO-TAB AT END                                 15020000
                                 GO TO 5000-RMP-INIT-EXIT               15030000
            WHEN WK-REPORT-KEY = REPORT-KEY (REPORT-INDEX)              15040000
              MOVE REPORT-NO(REPORT-INDEX) TO INT-REPORT-NO(1)          15050000
                                              H02-REPORT-NO             15060000
                                                                        15070000
              MOVE REPORT-NAME(REPORT-INDEX) TO H02-REPORT-NAME.        15080000
                                                                        15090000
            CALL 'RMPSUB' USING W-SEND.                                 15100000
                                                                        15110000
       5000-RMP-INIT-EXIT. EXIT.                                        15120000
      *-------------------------------------------------------*         15130000
       5100-PRT-HEADER.                                                 ********
                                                                        ********
            MOVE 7                       TO WK-LINE-NO.                 ********
            ADD  1                       TO WK-PAGE-NO.                 ********
            MOVE WK-PAGE-NO              TO H02-PAGE.                   ********
            IF FOUND-STMTDET                                            ********
               MOVE SPACES               TO H03-ACCOUNT                 ********
               MOVE SPACES               TO H03-ACCOUNT-NAME            ********
               MOVE SPACES               TO H03-S1-CORP-ID              ********
            ELSE                                                        ********
               MOVE ACCT-NO              TO H03-ACCOUNT                 ********
               MOVE ACCT-NAME            TO H03-ACCOUNT-NAME            ********
               MOVE ACCT-IN-CORP-ID      TO H03-S1-CORP-ID              ********
            END-IF.                                                     ********
            MOVE TODAY-DATE-YYYY         TO H03-RDATE-YYYY.             ********
            MOVE TODAY-DATE-MM           TO H03-RDATE-MM.               ********
            MOVE TODAY-DATE-DD           TO H03-RDATE-DD.               ********
            WRITE XCPT-OUT-REC FROM HEAD-01   AFTER NEXT-PAGE.          ********
            WRITE XCPT-OUT-REC FROM HEAD-02   AFTER ADVANCING 4.        ********
            WRITE XCPT-OUT-REC FROM HEAD-03   AFTER ADVANCING 4.        ********
            WRITE XCPT-OUT-REC FROM HEAD-LINE AFTER ADVANCING 4.        ********
            WRITE XCPT-OUT-REC FROM HEAD-04   AFTER ADVANCING 2.        ********
            WRITE XCPT-OUT-REC FROM HEAD-05   AFTER ADVANCING 2.        ********
            WRITE XCPT-OUT-REC FROM HEAD-LINE AFTER ADVANCING 1.        15400000
                                                                        15410000
            MOVE 2                       TO SERVICE-REQUEST-CODE.       15420000
            MOVE H02-REPORT-NO           TO TRF-REPORT-NUMBER.          15430000
            MOVE CENTER-BR               TO TRF-REPORT-TO-NUMBER.       15440000
            MOVE 99                      TO TRF-CHANNEL-CONTROL.        15450000
            MOVE HEAD-01                 TO TRF-REPORT-DATA.            15460000
            CALL 'RMPSUB' USING W-SEND.                                 15470000
            MOVE 2                       TO TRF-CHANNEL-CONTROL.        15480000
            MOVE HEAD-02                 TO TRF-REPORT-DATA.            15490000
            CALL 'RMPSUB' USING W-SEND.                                 15500000
            MOVE HEAD-03                 TO TRF-REPORT-DATA.            15510000
            CALL 'RMPSUB' USING W-SEND.                                 15520000
            MOVE 1                       TO TRF-CHANNEL-CONTROL.        15530000
            MOVE HEAD-LINE               TO TRF-REPORT-DATA.            15540000
            CALL 'RMPSUB' USING W-SEND.                                 15550000
            MOVE HEAD-04                 TO TRF-REPORT-DATA.            15560000
            CALL 'RMPSUB' USING W-SEND.                                 15570000
            MOVE 1                       TO TRF-CHANNEL-CONTROL.        15580000
            MOVE HEAD-05                 TO TRF-REPORT-DATA.            15590000
            CALL 'RMPSUB' USING W-SEND.                                 15600000
            MOVE HEAD-LINE               TO TRF-REPORT-DATA.            15610000
            CALL 'RMPSUB' USING W-SEND.                                 15620000
                                                                        15630000
       5100-EXIT. EXIT.                                                 15640000
      *-------------------------------------------------------*         15650000
       5101-PRT-HEADER.                                                 ********
                                                                        ********
            MOVE 7                       TO WK-LINE-NO.                 ********
            ADD  1                       TO WK-PAGE-NO.                 ********
            MOVE WK-PAGE-NO              TO H02-PAGE.                   ********
            IF FOUND-STMTDET                                            ********
               MOVE SPACES               TO H03-ACCOUNT                 ********
               MOVE SPACES               TO H03-ACCOUNT-NAME            ********
               MOVE SPACES               TO H03-S1-CORP-ID              ********
            ELSE                                                        ********
               MOVE ACCT-NO              TO H03-ACCOUNT                 ********
               MOVE ACCT-NAME            TO H03-ACCOUNT-NAME            ********
               MOVE ACCT-IN-CORP-ID      TO H03-S1-CORP-ID              ********
            END-IF.                                                     ********
            MOVE TODAY-DATE-YYYY         TO H03-RDATE-YYYY.             ********
            MOVE TODAY-DATE-MM           TO H03-RDATE-MM.               ********
            MOVE TODAY-DATE-DD           TO H03-RDATE-DD.               ********
            WRITE XCPT-OUT-REC FROM HEAD-01   AFTER NEXT-PAGE.          ********
            WRITE XCPT-OUT-REC FROM HEAD-02   AFTER ADVANCING 4.        ********
            WRITE XCPT-OUT-REC FROM HEAD-03   AFTER ADVANCING 4.        ********
            WRITE XCPT-OUT-REC FROM HEAD-LINE AFTER ADVANCING 4.        ********
            WRITE XCPT-OUT-REC FROM HEAD-04   AFTER ADVANCING 2.        ********
            WRITE XCPT-OUT-REC FROM HEAD-05   AFTER ADVANCING 2.        ********
            WRITE XCPT-OUT-REC FROM HEAD-LINE AFTER ADVANCING 1.        15920000
                                                                        15930000
            MOVE 2                       TO SERVICE-REQUEST-CODE.       15940000
            MOVE H02-REPORT-NO           TO TRF-REPORT-NUMBER.          15950000
            MOVE CENTER-BR               TO TRF-REPORT-TO-NUMBER.       15960000
            MOVE 99                      TO TRF-CHANNEL-CONTROL.        15970000
            MOVE HEAD-01                 TO TRF-REPORT-DATA.            15980000
            CALL 'RMPSUB' USING W-SEND.                                 15990000
            MOVE 2                       TO TRF-CHANNEL-CONTROL.        16000000
            MOVE HEAD-02                 TO TRF-REPORT-DATA.            16010000
            CALL 'RMPSUB' USING W-SEND.                                 16020000
            MOVE HEAD-03                 TO TRF-REPORT-DATA.            16030000
            CALL 'RMPSUB' USING W-SEND.                                 16040000
            MOVE 1                       TO TRF-CHANNEL-CONTROL.        16050000
            MOVE HEAD-LINE               TO TRF-REPORT-DATA.            16060000
            CALL 'RMPSUB' USING W-SEND.                                 16070000
            MOVE HEAD-04                 TO TRF-REPORT-DATA.            16080000
            CALL 'RMPSUB' USING W-SEND.                                 16090000
            MOVE 1                       TO TRF-CHANNEL-CONTROL.        16100000
            MOVE HEAD-05                 TO TRF-REPORT-DATA.            16110000
            CALL 'RMPSUB' USING W-SEND.                                 16120000
            MOVE HEAD-LINE               TO TRF-REPORT-DATA.            16130000
            CALL 'RMPSUB' USING W-SEND.                                 16140000
                                                                        16150000
       5101-EXIT. EXIT.                                                 16160000
      *-------------------------------------------------------*         16170000
       5200-MOVE-XCPTOUTF-1.                                            16180000
                                                                        16190000
            MOVE STMT-IN-SERIAL-NO     TO P01-TRAN-SEQ.                 16200000
            MOVE STMT-IN-DESC(2:3)     TO P01-PROD-CODE.                16210000
            MOVE STMT-IN-TDATE-DD      TO P01-TDATE-DD                  16220000
            MOVE STMT-IN-TDATE-MM      TO P01-TDATE-MM                  16230000
            MOVE STMT-IN-TDATE-YYYY    TO P01-TDATE-YYYY                16240000
            MOVE STMT-IN-SYMBOL        TO P01-STM-CODE.                 16280000
            MOVE STMT-IN-CHAN          TO P01-CHN-CODE.                 16290000
            MOVE STMT-IN-CRDR          TO P01-TRAN-TYPE.                16300000
            MOVE WK-STMT-IN-TAMT       TO P01-TRAN-AMT.                 16310000
            MOVE STMT-IN-DESC          TO P01-LINK-KEY.                 16320000
            MOVE SWK-S1-REF(1:20)      TO P01-S1-BAT-REF.               16330000
                                                                        16340000
       5200-1-EXIT. EXIT.                                               16350000
      *-------------------------------------------------------*         ********
       5200-MOVE-XCPTOUTF-2.                                            ********
                                                                        ********
            MOVE SWK-IN-TSEQ-NO        TO P02-SEQ-NO.                   ********
            MOVE '         '           TO P02-BEN-NAME.                 ********
            MOVE SPACES                TO P02-BEN-ACCT.                 ********
            MOVE STMT-IN-BANK-ID       TO P02-BEN-BANK-CODE.            ********
            MOVE WK-STMT-IN-TAMT       TO P02-NET-AMT.                  ********
            MOVE SPACES                TO P02-CHQ-NO.                   ********
            MOVE SWK-TRANS-STATUS      TO P02-STATUS.                   ********
            MOVE SWK-TRANS-DES         TO P02-ERROR-CODE.               ********
            MOVE SPACES                TO P02-CUST-REF.                 ********
                                                                        ********
       5200-2-EXIT. EXIT.                                               ********
      *------------------------------------------------------------     ********
       5201-MOVE-XCPTOUTF-1.                                            ********
                                                                        ********
            MOVE STMT-IN-SERIAL-NO     TO XCPT-P01-TRAN-SEQ.            ********
            MOVE STMT-IN-DESC(2:3)     TO XCPT-P01-PROD-CODE.           16540000
            MOVE STMT-IN-TDATE-YYYY    TO XCPT-P01-TDATE-YYYY.          16550000
            MOVE STMT-IN-TDATE-MM      TO XCPT-P01-TDATE-MM.            16560000
            MOVE STMT-IN-TDATE-DD      TO XCPT-P01-TDATE-DD.            16570000
            MOVE SPACES                TO XCPT-P01-VDATE-YYYY.          16580000
            MOVE SPACES                TO XCPT-P01-VDATE-MM.            16590000
            MOVE SPACES                TO XCPT-P01-VDATE-DD.            16600000
            MOVE STMT-IN-SYMBOL        TO XCPT-P01-STM-CODE.            16610000
            MOVE STMT-IN-CHAN          TO XCPT-P01-CHN-CODE.            16620000
            MOVE STMT-IN-CRDR          TO XCPT-P01-TRAN-TYPE.           16630000
            MOVE WK-STMT-IN-TAMT       TO XCPT-P01-TRAN-AMT.            16640000
            MOVE STMT-IN-DESC          TO XCPT-P01-LINK-KEY.            16650000
            MOVE SWK-S1-REF(1:20)      TO XCPT-P01-S1-BAT-REF.          16660000
                                                                        16670000
       5201-1-EXIT. EXIT.                                               16680000
      *-------------------------------------------------------*         16690000
       5201-MOVE-XCPTOUTF-2.                                            ********
                                                                        ********
            ADD  1                 TO TOT-CRDT.                         ********
            MOVE SWK-IN-TSEQ-NO    TO XCPT-P02-SEQ-NO(CRDT-IDX).        ********
            MOVE SWK-PAYEE-NAME-EN TO XCPT-P02-BEN-NAME(CRDT-IDX).      ********
            MOVE SWK-CR-ACCT       TO XCPT-P02-BEN-ACCT(CRDT-IDX).      ********
            MOVE SWK-RECEIVE-BANK-CD                                    ********
                                   TO XCPT-P02-BEN-BANK-CODE(CRDT-IDX). ********
            MOVE WK-CURR-TRANS-AMT TO XCPT-P02-NET-AMT(CRDT-IDX).       ********
            MOVE SWK-CHQ-NO        TO XCPT-P02-CHQ-NO(CRDT-IDX).        ********
            MOVE SWK-TRANS-STATUS  TO XCPT-P02-STATUS(CRDT-IDX).        ********
            MOVE SPACES            TO XCPT-P02-ERROR-CODE(CRDT-IDX).    ********
            MOVE SWK-CUST-REF(21:19)                                    ********
                                   TO XCPT-P02-CUST-REF(CRDT-IDX).      ********
            SET CRDT-IDX UP BY 1.                                       ********
                                                                        ********
       5201-2-EXIT. EXIT.                                               ********
      *-------------------------------------------------------*         ********
       5201-MOVE-XCPTOUTF-3.                                            ********
                                                                        ********
            ADD  1                TO TOT-CRDT.                          ********
            MOVE SWK-IN-TSEQ-NO   TO XCPT-P02-SEQ-NO(CRDT-IDX).         ********
            MOVE '         '      TO XCPT-P02-BEN-NAME(CRDT-IDX).       ********
            MOVE SPACES           TO XCPT-P02-BEN-ACCT(CRDT-IDX).       ********
            MOVE STMT-IN-BANK-ID  TO XCPT-P02-BEN-BANK-CODE(CRDT-IDX).  ********
            MOVE WK-DIFF-TAMT     TO XCPT-P02-NET-AMT(CRDT-IDX).        ********
            MOVE SPACES           TO XCPT-P02-CHQ-NO(CRDT-IDX).         ********
            MOVE SWK-TRANS-STATUS TO XCPT-P02-STATUS(CRDT-IDX).         ********
            MOVE SWK-TRANS-DES    TO XCPT-P02-ERROR-CODE(CRDT-IDX).     ********
            MOVE SPACES           TO XCPT-P02-CUST-REF(CRDT-IDX).       ********
            SET CRDT-IDX UP BY 1.                                       ********
                                                                        ********
       5201-3-EXIT. EXIT.                                               ********
      *-------------------------------------------------------*         ********
       5300-WRITE-XCPTOUTF.                                             ********
                                                                        ********
            IF WK-LINE-NO GREATER THAN 42 THEN                          17060000
               PERFORM 5100-PRT-HEADER   THRU 5100-EXIT                 17070000
            END-IF.                                                     17080000
            WRITE XCPT-OUT-REC FROM PRT-01 AFTER ADVANCING 2.           17090000
            WRITE XCPT-OUT-REC FROM PRT-02 AFTER ADVANCING 1.           17100000
            ADD  2                        TO WK-LINE-NO.                17110000
                                                                        17120000
            MOVE 2                        TO SERVICE-REQUEST-CODE.      17130000
            MOVE H02-REPORT-NO            TO TRF-REPORT-NUMBER.         17140000
            MOVE CENTER-BR                TO TRF-REPORT-TO-NUMBER.      17150000
            MOVE 2                        TO TRF-CHANNEL-CONTROL.       17160000
            MOVE PRT-01                   TO TRF-REPORT-DATA.           17170000
            CALL 'RMPSUB' USING W-SEND.                                 17180000
            MOVE 1                        TO TRF-CHANNEL-CONTROL.       17190000
            MOVE PRT-02                   TO TRF-REPORT-DATA.           17200000
            CALL 'RMPSUB' USING W-SEND.                                 17210000
                                                                        17220000
       5300-EXIT. EXIT.                                                 17230000
      *-------------------------------------------------------*         17240000
       5400-PRT-EOF-DATA.                                               17250000
                                                                        17260000
            IF WK-LINE-NO GREATER THAN 42 THEN                          17270000
               PERFORM 5100-PRT-HEADER   THRU 5100-EXIT                 17280000
            END-IF.                                                     17290000
            WRITE XCPT-OUT-REC FROM EOF-DATA   AFTER ADVANCING 2.       17300000
            WRITE XCPT-OUT-REC FROM HEAD-LINE  AFTER ADVANCING 1.       17310000
                                                                        17320000
            MOVE 1                   TO   TRF-CHANNEL-CONTROL.          17330000
            MOVE SPACE-LINE          TO   TRF-REPORT-DATA.              17340000
            CALL 'RMPSUB' USING W-SEND.                                 17350000
            MOVE HEAD-LINE           TO TRF-REPORT-DATA.                17360000
            CALL 'RMPSUB' USING W-SEND.                                 17370000
                                                                        17380000
       5400-EXIT. EXIT.                                                 17390000
      *-------------------------------------------------------*         17400000
       5500-PRT-NO-EXCEPTION.                                           17410000
                                                                        17420000
            MOVE '               *****  NO DATA ******                ' 17430000
                                     TO SPACE-NO-DATA.                  17440000
                                                                        17450000
            WRITE XCPT-OUT-REC FROM SPACE-LINE AFTER ADVANCING 1.       17460000
                                                                        17470000
            MOVE 1                   TO   TRF-CHANNEL-CONTROL.          17480000
            MOVE SPACE-LINE          TO   TRF-REPORT-DATA.              17490000
            CALL 'RMPSUB' USING W-SEND.                                 17500000
                                                                        17510000
            WRITE XCPT-OUT-REC FROM HEAD-LINE  AFTER ADVANCING 1.       17520000
                                                                        17530000
            MOVE 1                   TO TRF-CHANNEL-CONTROL.            17540000
            MOVE HEAD-LINE           TO TRF-REPORT-DATA.                17550000
            CALL 'RMPSUB' USING W-SEND.                                 17560000
                                                                        17570000
       5500-EXIT. EXIT.                                                 17580000
      *-------------------------------------------------------*         17590000
       5600-WRITE-XCPTOUTF.                                             17600000
                                                                        17610000
            IF WK-LINE-NO GREATER THAN 42 THEN                          17630000
               PERFORM 5100-PRT-HEADER   THRU 5100-EXIT                 17640000
            END-IF.                                                     17650000
                                                                        17660000
            MOVE XCPT-P01-TRAN-SEQ        TO P01-TRAN-SEQ.              17670000
            MOVE XCPT-P01-PROD-CODE       TO P01-PROD-CODE.             17680000
            MOVE XCPT-P01-TDATE-YYYY      TO P01-TDATE-YYYY.            17690000
            MOVE XCPT-P01-TDATE-MM        TO P01-TDATE-MM.              17700000
            MOVE XCPT-P01-TDATE-DD        TO P01-TDATE-DD.              17710000
            MOVE XCPT-P01-VDATE-YYYY      TO P01-VDATE-YYYY.            17720000
            MOVE XCPT-P01-VDATE-MM        TO P01-VDATE-MM.              17730000
            MOVE XCPT-P01-VDATE-DD        TO P01-VDATE-DD.              17740000
            MOVE XCPT-P01-STM-CODE        TO P01-STM-CODE.              17750000
            MOVE XCPT-P01-CHN-CODE        TO P01-CHN-CODE.              17760000
            MOVE XCPT-P01-TRAN-TYPE       TO P01-TRAN-TYPE.             17770000
            MOVE XCPT-P01-TRAN-AMT        TO P01-TRAN-AMT.              17780000
            MOVE XCPT-P01-LINK-KEY        TO P01-LINK-KEY.              17790000
            MOVE XCPT-P01-S1-BAT-REF      TO P01-S1-BAT-REF.            17800000
            WRITE XCPT-OUT-REC FROM PRT-01 AFTER ADVANCING 1.           17810000
            ADD  1                         TO WK-LINE-NO.               17820000
                                                                        17830000
            MOVE 2                         TO SERVICE-REQUEST-CODE.     17840000
            MOVE H02-REPORT-NO             TO TRF-REPORT-NUMBER.        17850000
            MOVE CENTER-BR                 TO TRF-REPORT-TO-NUMBER.     17860000
            MOVE 2                         TO TRF-CHANNEL-CONTROL.      17870000
            MOVE PRT-01                    TO TRF-REPORT-DATA.          17880000
            CALL 'RMPSUB' USING W-SEND.                                 17890000
            MOVE ZEROS                     TO J.                        ********
                                                                        ********
            PERFORM VARYING J FROM 1 BY 1 UNTIL J > TOT-CRDT            ********
               MOVE XCPT-P02-SEQ-NO(J)     TO P02-SEQ-NO                ********
               MOVE XCPT-P02-BEN-NAME(J)   TO P02-BEN-NAME              ********
               MOVE XCPT-P02-BEN-ACCT(J)   TO P02-BEN-ACCT              ********
               MOVE XCPT-P02-BEN-BANK-CODE(J) TO P02-BEN-BANK-CODE      ********
               MOVE XCPT-P02-NET-AMT(J)    TO P02-NET-AMT               ********
               MOVE XCPT-P02-CHQ-NO(J)     TO P02-CHQ-NO                ********
               MOVE XCPT-P02-STATUS(J)     TO P02-STATUS                ********
               MOVE XCPT-P02-ERROR-CODE(J) TO P02-ERROR-CODE            ********
               MOVE XCPT-P02-CUST-REF(J)   TO P02-CUST-REF              ********
               WRITE XCPT-OUT-REC FROM PRT-02 AFTER ADVANCING 1         ********
               ADD  1                      TO WK-LINE-NO                ********
               IF WK-LINE-NO GREATER THAN 42 THEN                       ********
                  PERFORM 5100-PRT-HEADER   THRU 5100-EXIT              ********
               END-IF                                                   ********
               MOVE 1                      TO TRF-CHANNEL-CONTROL       ********
               MOVE PRT-02                 TO TRF-REPORT-DATA           ********
               CALL 'RMPSUB' USING W-SEND                               18090000
           END-PERFORM.                                                 18100000
                                                                        18110000
       5600-EXIT. EXIT.                                                 18120000
      *-------------------------------------------------------*         18130000
       5900-RMP-TERM-RTN.                                               18140000
            MOVE 3                    TO SERVICE-REQUEST-CODE.          18150000
            CALL 'RMPSUB' USING W-SEND.                                 18160000
                                                                        18170000
       5900-RMP-TERM-EXIT. EXIT.                                        18180000
      *-------------------------------------------------------*         18190000
       9999-CLOSEFL-RTN.                                                18200000
                                                                        18210000
           PERFORM 5900-RMP-TERM-RTN         THRU 5900-RMP-TERM-EXIT.   18220000
      *** CLOSE FILE                                                    18230000
      *    CLOSE DATE-IN-FL.                                            18240000
                                                                        18250000
           CLOSE ACCT-IN-FL.                                            18260000
           IF ACCT-IN-STAT NOT = '00'                                   18270000
              DISPLAY '*** PROGRAM STMMCG05 ***'  UPON CONSOLE          18280045
              DISPLAY 'CLOSE ACCTINF IN ERROR,CODE = ' ACCT-IN-STAT     18290000
                                                  UPON CONSOLE          18300000
              DISPLAY 'CLOSE ACCTINF IN ERROR,CODE = ' ACCT-IN-STAT     18310000
           END-IF.                                                      18320000
                                                                        18330000
           CLOSE STMT-IN-FL.                                            18340000
           IF STMT-IN-STAT NOT = '00' AND                               18350000
              STMT-IN-STAT NOT = '42'                                   18360000
              DISPLAY '*** PROGRAM STMMCG05 ***'  UPON CONSOLE          18370045
              DISPLAY 'CLOSE STMTINF IN ERROR,CODE = ' STMT-IN-STAT     18380000
                                                  UPON CONSOLE          18390000
              DISPLAY 'CLOSE STMTINF IN ERROR,CODE = ' STMT-IN-STAT     18400000
           END-IF.                                                      18410000
                                                                        18420000
           CLOSE DRBT-IO-FL.                                            18430000
           IF DRBT-IO-STAT NOT = '00' AND                               18440000
              DRBT-IO-STAT NOT = '42'                                   18450000
              DISPLAY '*** PROGRAM STMMCG05 ***'  UPON CONSOLE          18460045
              DISPLAY 'CLOSE DRBTIOF ERROR,CODE = ' DRBT-IO-STAT        18470000
                                                  UPON CONSOLE          18480000
              DISPLAY 'CLOSE DRBTIOF ERROR,CODE = ' DRBT-IO-STAT        18490000
           END-IF.                                                      18500000
                                                                        18510000
           CLOSE CRDT-IO-FL.                                            18520000
           IF CRDT-IO-STAT NOT = '00' AND                               18530000
              CRDT-IO-STAT NOT = '42'                                   18540000
              DISPLAY '*** PROGRAM STMMCG05 ***'  UPON CONSOLE          18550045
              DISPLAY 'CLOSE CRDTIOF ERROR,CODE = ' CRDT-IO-STAT        18560000
                                                  UPON CONSOLE          18570000
              DISPLAY 'CLOSE CRDTIOF ERROR,CODE = ' CRDT-IO-STAT        18580000
           END-IF.                                                      18590000
                                                                        18600000
           CLOSE STMT-OUTD-FL.                                          18610000
           IF STMT-OUTD-STAT NOT = '00'                                 18620000
              DISPLAY '*** PROGRAM STMMCG05 ***'      UPON CONSOLE      18630045
              DISPLAY 'CLOSE STMTOUTD ERROR,CODE  = ' STMT-OUTD-STAT    18640000
                                                      UPON CONSOLE      18650000
              DISPLAY 'CLOSE STMTOUTD ERROR,CODE  = ' STMT-OUTD-STAT    18660000
           END-IF.                                                      18670000
                                                                        18680000
           CLOSE XCPT-OUT-FL.                                           18690000
           IF XCPT-OUT-STAT NOT = '00'                                  18700000
              DISPLAY '*** PROGRAM STMMCG05 ***'      UPON CONSOLE      18710045
              DISPLAY 'CLOSE XCPTOUTF ERROR,CODE  = ' XCPT-OUT-STAT     18720000
                                                      UPON CONSOLE      18730000
              DISPLAY 'CLOSE XCPTOUTF ERROR,CODE  = ' XCPT-OUT-STAT     18740000
           END-IF.                                                      18750000
                                                                        18760000
       9999-EXIT.    EXIT.                                              18770000
      *-------------------- END PROGRAM -------------------*            18780000
/*                                                                      18790000
//LKED.SYSLIB   DD  DSN=&LIBPRFX..SCEELKED,DISP=SHR                     18800000
//              DD  DSN=LIBRBCM.USERACC.AP.BTCHLOAD,DISP=SHR            18810091
//LKED.SYSLMOD  DD  DSN=LIBRBCM.USERACC.AP.BTCHLOAD(STMMCG05),DISP=SHR  18820091
//LKED.SYSPRINT DD  SYSOUT=X                                            18830000
/*                                                                      18840000