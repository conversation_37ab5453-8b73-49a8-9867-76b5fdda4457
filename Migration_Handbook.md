# COBOL to Java Spring Boot Migration Handbook

## Table of Contents
1. [Executive Summary](#executive-summary)
2. [High-Level Architecture](#high-level-architecture)
3. [Microservices Design](#microservices-design)
4. [UML and Sequence Diagrams](#uml-and-sequence-diagrams)
5. [Class and Interface Definitions](#class-and-interface-definitions)
6. [Business Logic Implementation](#business-logic-implementation)
7. [COBOL to Java Mapping Table](#cobol-to-java-mapping-table)
8. [Migration Strategy and Sequencing](#migration-strategy-and-sequencing)
9. [Implementation Checklist](#implementation-checklist)
10. [Testing Strategy](#testing-strategy)
11. [Deployment Guidelines](#deployment-guidelines)

## Executive Summary

This handbook provides comprehensive guidance for migrating a 22-file COBOL banking statement processing system to a modern Java Spring Boot microservices architecture. The migration focuses on preserving critical business logic while modernizing the technology stack to be platform-agnostic and cloud-ready.

### Key Migration Principles
- **Business Logic First**: Preserve all critical business rules and calculations
- **Platform Agnostic**: Design for any cloud, infrastructure, or platform
- **Microservices Architecture**: Decompose into independent, scalable services
- **Modern Technology Stack**: Java 21, Spring Boot 3.x, NoSQL databases
- **Comprehensive Testing**: Automated testing to ensure functionality preservation

## High-Level Architecture

### Target Architecture Overview

```
┌─────────────────────────────────────────────────────────────────┐
│                        API Gateway                               │
│                   (Spring Cloud Gateway)                         │
└─────────────────────────┬───────────────────────────────────────┘
                          │
      ┌───────────────────┼───────────────────┐
      │                   │                   │
┌─────▼─────┐    ┌────────▼────────┐    ┌─────▼─────┐
│  Account  │    │   Payment       │    │Statement  │
│  Service  │    │ Processing      │    │ Service   │
│           │    │   Service       │    │           │
└─────┬─────┘    └────────┬────────┘    └─────┬─────┘
      │                   │                   │
┌─────▼─────┐    ┌────────▼────────┐    ┌─────▼─────┐
│Reference  │    │Bill Payment     │    │   EPP     │
│Data       │    │Service          │    │ Service   │
│Service    │    │                 │    │           │
└─────┬─────┘    └────────┬────────┘    └─────┬─────┘
      │                   │                   │
      └───────────────────┼───────────────────┘
                          │
              ┌───────────▼───────────┐
              │    Message Queue      │
              │   (Apache Kafka)      │
              └───────────┬───────────┘
                          │
              ┌───────────▼───────────┐
              │     Database Layer    │
              │  (PostgreSQL/MongoDB) │
              └───────────────────────┘
```

### Core Components

#### 1. API Gateway
- **Technology**: Spring Cloud Gateway
- **Purpose**: Request routing, load balancing, authentication
- **Features**: Rate limiting, circuit breaker, monitoring

#### 2. Microservices Layer
- **Account Service**: Profile management and validation
- **Payment Processing Service**: Core CN/IPS payment logic
- **Bill Payment Service**: Bill payment processing and validation
- **EPP Service**: Electronic payment processing
- **LCC Service**: Local clearing collection processing
- **RFT Service**: Real-time fund transfers (PromptPay)
- **Statement Service**: Statement generation and formatting
- **Reference Data Service**: Lookup tables and caching

#### 3. Data Layer
- **Transactional Database**: PostgreSQL for ACID compliance
- **Document Database**: MongoDB for configuration and logs
- **Cache Layer**: Redis for performance optimization
- **Message Queue**: Apache Kafka for event-driven architecture

## Microservices Design

### Service Boundaries and Responsibilities

#### Account Service
```java
@RestController
@RequestMapping("/api/v1/accounts")
public class AccountController {
    // Account profile management
    // VSAM file replacement logic
    // Account validation and filtering
}
```

**Responsibilities**:
- Account profile management (replaces EBCMMC01)
- Account filtering logic (replaces STMBDD06/07)
- Profile validation and caching

#### Payment Processing Service (Core Service)
```java
@RestController
@RequestMapping("/api/v1/payments")
public class PaymentController {
    // Core CN/IPS payment processing
    // Product code validation
    // Transaction status management
}
```

**Responsibilities**:
- CN/IPS payment processing (replaces STMMCG05)
- Product code validation and mapping
- Transaction status management
- Exception handling and audit logging

#### Bill Payment Service
```java
@RestController
@RequestMapping("/api/v1/billpayments")
public class BillPaymentController {
    // Bill payment processing
    // PromptPay BILLER-ID support
    // Statement-detail matching
}
```

**Responsibilities**:
- Bill payment processing (replaces STMBDD08, STMMCG02)
- PromptPay integration
- Payment reconciliation and matching

## UML and Sequence Diagrams

### High-Level System Sequence Diagram

```mermaid
sequenceDiagram
    participant Client
    participant Gateway
    participant AccountSvc
    participant PaymentSvc
    participant StatementSvc
    participant Database
    participant Queue

    Client->>Gateway: Process Payment Request
    Gateway->>AccountSvc: Validate Account
    AccountSvc->>Database: Get Account Profile
    Database-->>AccountSvc: Account Data
    AccountSvc-->>Gateway: Account Valid
    
    Gateway->>PaymentSvc: Process Payment
    PaymentSvc->>Database: Validate Transaction
    PaymentSvc->>Queue: Publish Payment Event
    Queue-->>StatementSvc: Payment Processed
    StatementSvc->>Database: Generate Statement
    
    PaymentSvc-->>Gateway: Payment Successful
    Gateway-->>Client: Payment Response
```

### Payment Processing Flow Diagram

```mermaid
graph TD
    A[Payment Request] --> B{Account Valid?}
    B -->|No| C[Reject Payment]
    B -->|Yes| D[Validate Product Code]
    D --> E{Product Code Valid?}
    E -->|No| F[Exception Handling]
    E -->|Yes| G[Process Transaction]
    G --> H{Transaction Status}
    H -->|Success| I[Generate Statement]
    H -->|Failed| J[Rollback Transaction]
    I --> K[Audit Log]
    J --> K
    K --> L[Return Response]
```

### Data Flow Architecture

```mermaid
graph LR
    A[ERP System] --> B[Account Service]
    B --> C[Payment Processing]
    C --> D[Bill Payment Service]
    D --> E[EPP Service]
    E --> F[LCC Service]
    F --> G[RFT Service]
    G --> H[Statement Service]
    H --> I[Output Generation]
    
    B --> J[(Account DB)]
    C --> K[(Transaction DB)]
    H --> L[(Statement DB)]
```

## Class and Interface Definitions

### Core Domain Models

#### Account Domain
```java
package com.scb.bizo.domain.account;

@Entity
@Table(name = "account_profiles")
public class AccountProfile {
    @Id
    private String accountNumber;
    private String accountType;
    private String productCode;
    private BigDecimal balance;
    private LocalDateTime lastUpdated;
    private String status;
    
    // Constructors, getters, setters
}

@Repository
public interface AccountProfileRepository extends JpaRepository<AccountProfile, String> {
    List<AccountProfile> findByAccountTypeAndStatus(String accountType, String status);
    Optional<AccountProfile> findByAccountNumberAndStatus(String accountNumber, String status);
}

@Service
public class AccountService {
    // Business logic for account management
    public AccountProfile validateAccount(String accountNumber);
    public List<AccountProfile> filterAccountsByProfile(String profileType);
}
```

#### Payment Domain
```java
package com.scb.bizo.domain.payment;

@Entity
@Table(name = "transactions")
public class Transaction {
    @Id
    private String transactionId;
    private String accountNumber;
    private String productCode;
    private BigDecimal amount;
    private TransactionStatus status;
    private PaymentType paymentType;
    private LocalDateTime processedDate;
    private String referenceNumber;
    
    // Constructors, getters, setters
}

public enum TransactionStatus {
    PENDING("P"),
    COMPLETED("C"),
    CANCELLED_BEFORE_DEBIT("C"),
    CANCELLED_AFTER_DEBIT("J"),
    FAILED("F");
    
    private final String code;
    // Constructor and methods
}

public enum PaymentType {
    INTERBANK("IB"),
    BILL_PAYMENT("BP"),
    EPP("EP"),
    LCC("LC"),
    CN_IPS("CI"),
    RFT_PROMPTPAY("RP");
    
    private final String code;
    // Constructor and methods
}

@Service
public class PaymentProcessingService {
    public TransactionResult processPayment(PaymentRequest request);
    public void validateProductCode(String productCode);
    public void handleTransactionStatus(Transaction transaction);
}
```

#### Statement Domain
```java
package com.scb.bizo.domain.statement;

@Entity
@Table(name = "statements")
public class Statement {
    @Id
    private String statementId;
    private String accountNumber;
    private String statementType;
    private String content;
    private LocalDateTime generatedDate;
    private Integer recordLength;
    
    // Constructors, getters, setters
}

@Service
public class StatementService {
    public Statement generateStatement(String accountNumber, StatementType type);
    public String formatStatementContent(Transaction transaction, StatementType type);
    public void mergeStatements(List<Statement> statements);
}
```

### Service Interfaces

#### Core Service Interfaces
```java
package com.scb.bizo.service;

public interface PaymentProcessor {
    TransactionResult processPayment(PaymentRequest request);
    void validateTransaction(Transaction transaction);
    void auditTransaction(Transaction transaction);
}

public interface StatementGenerator {
    Statement generateStatement(StatementRequest request);
    String formatContent(Transaction transaction, StatementFormat format);
    void publishStatement(Statement statement);
}

public interface ReferenceDataService {
    Optional<ReferenceData> lookupByCode(String code, String type);
    void cacheReferenceData(String type);
    void refreshCache();
}
```

## Business Logic Implementation

### Core Business Rules

#### Product Code Validation (from STMMCG05)
```java
@Component
public class ProductCodeValidator {
    
    private static final Map<String, String> PRODUCT_CODE_MAPPING = Map.of(
        "DCP", "BNT",
        "EWT", "EWT",
        "IPS", "IPS"
    );
    
    public boolean validateProductCode(String productCode) {
        return PRODUCT_CODE_MAPPING.containsKey(productCode) ||
               isValidEwtProductCode(productCode);
    }
    
    private boolean isValidEwtProductCode(String productCode) {
        // EWT product code validation logic
        return productCode != null && productCode.startsWith("EWT");
    }
    
    public String mapProductCode(String originalCode) {
        return PRODUCT_CODE_MAPPING.getOrDefault(originalCode, originalCode);
    }
}
```

#### Transaction Status Management
```java
@Component
public class TransactionStatusManager {
    
    public void updateTransactionStatus(Transaction transaction, String statusCode) {
        switch (statusCode) {
            case "C":
                if (transaction.getDebitDate() == null) {
                    transaction.setStatus(TransactionStatus.CANCELLED_BEFORE_DEBIT);
                } else {
                    transaction.setStatus(TransactionStatus.CANCELLED_AFTER_DEBIT);
                }
                break;
            case "J":
                transaction.setStatus(TransactionStatus.CANCELLED_AFTER_DEBIT);
                break;
            default:
                transaction.setStatus(TransactionStatus.COMPLETED);
        }
    }
}
```

#### Amount Formatting and Validation
```java
@Component
public class AmountProcessor {
    
    public BigDecimal formatAmount(String amountString) {
        // Remove leading zeros and format to BigDecimal
        String cleanAmount = amountString.replaceFirst("^0+", "");
        if (cleanAmount.isEmpty()) {
            return BigDecimal.ZERO;
        }
        return new BigDecimal(cleanAmount).divide(BigDecimal.valueOf(100));
    }
    
    public String formatAmountForOutput(BigDecimal amount, int length) {
        // Format amount to fixed-length string with leading zeros
        long amountInCents = amount.multiply(BigDecimal.valueOf(100)).longValue();
        return String.format("%0" + length + "d", amountInCents);
    }
}
```

#### Date Processing Utilities
```java
@Component
public class DateProcessor {
    
    private static final DateTimeFormatter COBOL_DATE_FORMAT = 
        DateTimeFormatter.ofPattern("yyyyMMdd");
    
    public LocalDate parseCobolDate(String cobolDate) {
        return LocalDate.parse(cobolDate, COBOL_DATE_FORMAT);
    }
    
    public String formatToCobolDate(LocalDate date) {
        return date.format(COBOL_DATE_FORMAT);
    }
    
    public String getCurrentDateFormatted() {
        return LocalDate.now().format(COBOL_DATE_FORMAT);
    }
}
```

## COBOL to Java Mapping Table

### Complete Program Mapping

| COBOL Program | Java Component | Responsibility | Priority |
|---------------|----------------|----------------|----------|
| **EBCMMC01** | AccountService.processErpProfiles() | ERP account profile processing | High |
| **EBCMMC02** | AccountService.processStatements() | Statement processing coordination | High |
| **EBCMMC03** | PaymentService.processInterbankStatements() | Interbank statement processing | High |
| **EBCMMC04** | StatementService.mergeStatements() | Statement merging logic | High |
| **EBCMMC05** | BillPaymentService.processBillPayments() | Bill payment processing | High |
| **EBCMMC06** | EppService.processEppDetails() | EPP processing | Medium |
| **EBCMMC07** | LccService.processLccDetails() | LCC processing | Medium |
| **EBCMMC08** | PaymentService.processCnIpsPayments() | CN/IPS payment processing | High |
| **EBCMMC09** | StatementService.generateReferenceStatements() | Reference statement generation | High |
| **EBCMMC71** | RftService.processPromptPayTransfers() | PromptPay/RFT processing | Medium |
| **EBCMAFTB** | JobSchedulerService.triggerBatchJobs() | Job orchestration | Low |
| **STMBDD06** | AccountService.filterAccountsByProfile() | Account filtering | Medium |
| **STMBDD07** | AccountService.filterSimpleStatements() | Simple statement filtering | Low |
| **STMBDD08** | BillPaymentService.processPaymentDetails() | Bill payment detail processing | Medium |
| **STMMCG01** | StatementService.mergeInterbankStatements() | Interbank statement merging | High |
| **STMMCG02** | BillPaymentService.generateStatements() | Bill payment statement generation | Medium |
| **STMMCG03** | EppService.processStatements() | EPP statement processing | Medium |
| **STMMCG04** | LccService.processStatements() | LCC statement processing | Medium |
| **STMMCG05** | PaymentService.processCnIpsCore() | **Core CN/IPS processing** | **Critical** |
| **STMMCG06** | RftService.processRealTimeTransfers() | Real-time transfer processing | Medium |
| **STMMCG07** | PaymentService.processOutwardPayments() | Outward payment processing | High |
| **STMMCREF** | ReferenceDataService.processReferences() | Reference data processing | High |

### Data Structure Mapping

| COBOL Data Structure | Java Entity | Key Fields | Notes |
|---------------------|-------------|-------------|-------|
| Account Profile Record | AccountProfile | accountNumber, accountType, productCode, balance | 350-character fixed record |
| Statement Record | Statement | statementId, accountNumber, content, recordLength | Variable length (175-3200 chars) |
| Transaction Record | Transaction | transactionId, amount, status, paymentType | Core transaction entity |
| Reference Data Record | ReferenceData | code, type, description, value | Lookup table replacement |
| Bill Payment Record | BillPayment | billerId, paymentAmount, paymentDate | 285-character record |
| EPP Record | EppTransaction | eppId, transactionType, amount | 700-character record |
| LCC Record | LccTransaction | lccId, transactionType, amount | 1300-character record |

### Business Rule Mapping

| COBOL Business Rule | Java Implementation | Service | Priority |
|---------------------|-------------------|---------|----------|
| Product code validation (STMMCG05) | ProductCodeValidator.validateProductCode() | PaymentService | Critical |
| Transaction status management | TransactionStatusManager.updateStatus() | PaymentService | Critical |
| Amount formatting | AmountProcessor.formatAmount() | Common Utilities | High |
| Date conversion | DateProcessor.parseCobolDate() | Common Utilities | High |
| Exception handling | ExceptionHandler.handlePaymentException() | PaymentService | High |
| Statement formatting | StatementFormatter.formatContent() | StatementService | High |
| Reference data lookup | ReferenceDataService.lookupByCode() | ReferenceDataService | Medium |
| Account filtering | AccountFilter.filterByProfile() | AccountService | Medium |

## Migration Strategy and Sequencing

### Phase 1: Foundation (Weeks 1-4)
**Objective**: Establish core infrastructure and data models

**Tasks**:
1. **Database Schema Design**
   - Create entity models for all COBOL record structures
   - Implement Spring Data JPA repositories
   - Set up database migration scripts
   - Create test data sets

2. **Core Service Framework**
   - Set up Spring Boot project structure
   - Implement base service classes and interfaces
   - Create common utilities (date processing, amount formatting)
   - Set up logging and monitoring

3. **Reference Data Service**
   - Implement ReferenceDataService (replaces STMMCREF)
   - Create caching layer with Redis
   - Implement lookup table management
   - Unit tests for reference data operations

**Deliverables**:
- Working database schema
- Core service framework
- Reference data service with caching
- Comprehensive unit tests

### Phase 2: Account Management (Weeks 5-6)
**Objective**: Implement account profile management and filtering

**Tasks**:
1. **Account Service Implementation**
   - Implement AccountService (replaces EBCMMC01, STMBDD06/07)
   - Account profile processing and validation
   - Account filtering logic
   - Integration with reference data service

2. **Testing and Validation**
   - Unit tests for account operations
   - Integration tests with database
   - Performance testing for account filtering

**Deliverables**:
- Complete AccountService implementation
- Account filtering and validation logic
- Integration tests and performance benchmarks

### Phase 3: Core Payment Processing (Weeks 7-12)
**Objective**: Implement the critical CN/IPS payment processing logic

**Tasks**:
1. **Payment Service Core Implementation**
   - Implement PaymentService (replaces STMMCG05 - most critical)
   - Product code validation and mapping
   - Transaction status management
   - Exception handling and audit logging

2. **Payment Processing Logic**
   - Complex business rule implementation
   - Amount formatting and validation
   - Status-based transaction processing
   - Error handling and recovery

3. **Comprehensive Testing**
   - Unit tests for all payment scenarios
   - Integration tests with account service
   - Load testing for payment processing
   - Business rule validation tests

**Deliverables**:
- Complete PaymentService implementation
- Core payment processing logic
- Comprehensive test suite
- Performance benchmarks

### Phase 4: Statement Processing (Weeks 13-16)
**Objective**: Implement statement generation and merging logic

**Tasks**:
1. **Statement Service Implementation**
   - Implement StatementService (replaces STMMCG01, EBCMMC04, EBCMMC09)
   - Statement generation and formatting
   - Statement merging logic
   - Output format handling

2. **Statement Processing Logic**
   - Multi-format statement generation
   - Statement merging algorithms
   - Reference statement processing
   - Outward payment statement generation

3. **Integration Testing**
   - End-to-end statement generation tests
   - Integration with payment service
   - Format validation and compliance tests

**Deliverables**:
- Complete StatementService implementation
- Statement generation and merging logic
- Format validation and compliance tests

### Phase 5: Specialized Payment Services (Weeks 17-22)
**Objective**: Implement specialized payment processing services

**Tasks**:
1. **Bill Payment Service**
   - Implement BillPaymentService (replaces EBCMMC05, STMBDD08, STMMCG02)
   - Bill payment processing and validation
   - PromptPay BILLER-ID support
   - Statement-detail matching logic

2. **EPP Service**
   - Implement EppService (replaces EBCMMC06, STMMCG03)
   - Electronic payment processing
   - EPP statement processing
   - EBPP detail generation

3. **LCC Service**
   - Implement LccService (replaces EBCMMC07, STMMCG04)
   - Local clearing collection processing
   - Complex transaction type matching
   - DR/CR transaction processing

4. **RFT Service**
   - Implement RftService (replaces EBCMMC71, STMMCG06)
   - Real-time fund transfer processing
   - PromptPay integration
   - Real-time transaction validation

**Deliverables**:
- Complete specialized payment services
- Integration tests for all payment types
- Performance benchmarks for each service

### Phase 6: Job Orchestration and Integration (Weeks 23-26)
**Objective**: Implement job orchestration and system integration

**Tasks**:
1. **Job Scheduler Service**
   - Implement JobSchedulerService (replaces EBCMAFTB and JCL files)
   - Spring Boot scheduler integration
   - Job dependency management
   - Error handling and retry logic

2. **Message Queue Integration**
   - Implement Apache Kafka integration
   - Event-driven architecture implementation
   - Async processing for bulk operations
   - Message persistence and replay

3. **API Gateway Setup**
   - Spring Cloud Gateway configuration
   - Load balancing and circuit breaker
   - Authentication and authorization
   - Rate limiting and monitoring

4. **End-to-End Integration Testing**
   - Complete workflow testing
   - Performance testing under load
   - Failure scenario testing
   - Security testing

**Deliverables**:
- Complete job orchestration system
- Message queue integration
- API Gateway with full security
- End-to-end system validation

### Phase 7: Testing and Optimization (Weeks 27-30)
**Objective**: Comprehensive testing and performance optimization

**Tasks**:
1. **Comprehensive Testing**
   - Business logic validation against COBOL system
   - Performance testing and optimization
   - Security testing and vulnerability assessment
   - Disaster recovery testing

2. **Performance Optimization**
   - Database query optimization
   - Caching strategy implementation
   - Connection pool tuning
   - Memory usage optimization

3. **Documentation and Training**
   - Complete API documentation
   - Deployment guides
   - Operational procedures
   - Developer training materials

**Deliverables**:
- Fully tested and optimized system
- Complete documentation suite
- Deployment and operational procedures
- Training materials and knowledge transfer

## Implementation Checklist

### Pre-Migration Checklist
- [ ] **Environment Setup**
  - [ ] Java 21 development environment
  - [ ] Spring Boot 3.x project structure
  - [ ] PostgreSQL database setup
  - [ ] Redis cache setup
  - [ ] Apache Kafka setup
  - [ ] Docker containerization
  - [ ] CI/CD pipeline setup

- [ ] **Analysis Completion**
  - [ ] All 22 COBOL files analyzed
  - [ ] Business rules documented
  - [ ] Data structures mapped
  - [ ] Integration points identified
  - [ ] Performance requirements defined

### Phase 1: Foundation
- [ ] **Database Schema**
  - [ ] All entity models created
  - [ ] Database migrations scripts
  - [ ] Test data sets prepared
  - [ ] Schema validation tests

- [ ] **Core Framework**
  - [ ] Spring Boot project structure
  - [ ] Base service classes
  - [ ] Common utilities implemented
  - [ ] Logging and monitoring setup

- [ ] **Reference Data Service**
  - [ ] ReferenceDataService implementation
  - [ ] Caching layer with Redis
  - [ ] Lookup table management
  - [ ] Unit tests (>90% coverage)

### Phase 2: Account Management
- [ ] **Account Service**
  - [ ] AccountService implementation
  - [ ] Account filtering logic
  - [ ] Profile validation
  - [ ] Integration with reference data

- [ ] **Testing**
  - [ ] Unit tests (>90% coverage)
  - [ ] Integration tests
  - [ ] Performance benchmarks
  - [ ] Business rule validation

### Phase 3: Core Payment Processing
- [ ] **Payment Service Core**
  - [ ] PaymentService implementation (STMMCG05 replacement)
  - [ ] Product code validation
  - [ ] Transaction status management
  - [ ] Exception handling

- [ ] **Business Logic**
  - [ ] All COBOL business rules implemented
  - [ ] Amount formatting and validation
  - [ ] Status-based processing
  - [ ] Audit logging

- [ ] **Testing**
  - [ ] Unit tests (>95% coverage)
  - [ ] Integration tests
  - [ ] Load testing
  - [ ] Business rule validation

### Phase 4: Statement Processing
- [ ] **Statement Service**
  - [ ] StatementService implementation
  - [ ] Statement generation
  - [ ] Statement merging
  - [ ] Format handling

- [ ] **Processing Logic**
  - [ ] Multi-format generation
  - [ ] Merging algorithms
  - [ ] Reference processing
  - [ ] Output validation

- [ ] **Testing**
  - [ ] End-to-end tests
  - [ ] Format validation
  - [ ] Integration tests
  - [ ] Compliance tests

### Phase 5: Specialized Services
- [ ] **Bill Payment Service**
  - [ ] BillPaymentService implementation
  - [ ] PromptPay integration
  - [ ] Statement-detail matching
  - [ ] Validation logic

- [ ] **EPP Service**
  - [ ] EppService implementation
  - [ ] Electronic payment processing
  - [ ] Statement processing
  - [ ] EBPP detail generation

- [ ] **LCC Service**
  - [ ] LccService implementation
  - [ ] Transaction type matching
  - [ ] DR/CR processing
  - [ ] Complex business rules

- [ ] **RFT Service**
  - [ ] RftService implementation
  - [ ] Real-time processing
  - [ ] PromptPay integration
  - [ ] Transaction validation

### Phase 6: Integration
- [ ] **Job Orchestration**
  - [ ] JobSchedulerService implementation
  - [ ] Dependency management
  - [ ] Error handling
  - [ ] Retry logic

- [ ] **Message Queue**
  - [ ] Kafka integration
  - [ ] Event-driven architecture
  - [ ] Async processing
  - [ ] Message persistence

- [ ] **API Gateway**
  - [ ] Gateway configuration
  - [ ] Load balancing
  - [ ] Security implementation
  - [ ] Monitoring setup

### Phase 7: Final Validation
- [ ] **Comprehensive Testing**
  - [ ] Business logic validation
  - [ ] Performance testing
  - [ ] Security testing
  - [ ] Disaster recovery testing

- [ ] **Optimization**
  - [ ] Database optimization
  - [ ] Caching optimization
  - [ ] Performance tuning
  - [ ] Memory optimization

- [ ] **Documentation**
  - [ ] API documentation
  - [ ] Deployment guides
  - [ ] Operational procedures
  - [ ] Training materials

### Functionality Validation Checklist

#### Core Business Functions
- [ ] **Account Management**
  - [ ] ERP account profile processing (EBCMMC01)
  - [ ] Account filtering by profile (STMBDD06)
  - [ ] Simple statement filtering (STMBDD07)
  - [ ] Account validation and caching

- [ ] **Payment Processing**
  - [ ] CN/IPS payment processing (STMMCG05)
  - [ ] Product code validation and mapping
  - [ ] Transaction status management
  - [ ] Exception handling and audit logging
  - [ ] Outward payment processing (STMMCG07)

- [ ] **Statement Generation**
  - [ ] Statement merging (STMMCG01)
  - [ ] Reference statement generation (STMMCREF)
  - [ ] Multi-format statement output
  - [ ] Statement consolidation rules

- [ ] **Specialized Payments**
  - [ ] Bill payment processing (STMBDD08, STMMCG02)
  - [ ] PromptPay BILLER-ID support
  - [ ] EPP processing (STMMCG03)
  - [ ] LCC processing (STMMCG04)
  - [ ] RFT/PromptPay processing (STMMCG06)

#### Data Processing Functions
- [ ] **Data Validation**
  - [ ] Amount formatting and validation
  - [ ] Date format conversion
  - [ ] Product code validation
  - [ ] Transaction type validation

- [ ] **Data Transformation**
  - [ ] COBOL to Java data mapping
  - [ ] Fixed-length record processing
  - [ ] Character encoding handling
  - [ ] Numeric precision preservation

- [ ] **Integration Functions**
  - [ ] ERP system integration
  - [ ] External payment system integration
  - [ ] Statement output generation
  - [ ] Audit trail generation

## Testing Strategy

### Testing Pyramid Implementation

#### Unit Tests (70% of tests)
```java
@ExtendWith(MockitoExtension.class)
class PaymentServiceTest {
    
    @Mock
    private PaymentRepository paymentRepository;
    
    @Mock
    private ProductCodeValidator productCodeValidator;
    
    @InjectMocks
    private PaymentService paymentService;
    
    @Test
    void shouldProcessValidPayment() {
        // Given
        PaymentRequest request = createValidPaymentRequest();
        when(productCodeValidator.validateProductCode(any())).thenReturn(true);
        
        // When
        TransactionResult result = paymentService.processPayment(request);
        
        // Then
        assertThat(result.isSuccess()).isTrue();
        verify(paymentRepository).save(any(Transaction.class));
    }
    
    @Test
    void shouldRejectInvalidProductCode() {
        // Given
        PaymentRequest request = createInvalidProductCodeRequest();
        when(productCodeValidator.validateProductCode(any())).thenReturn(false);
        
        // When & Then
        assertThatThrownBy(() -> paymentService.processPayment(request))
            .isInstanceOf(InvalidProductCodeException.class);
    }
}
```

#### Integration Tests (20% of tests)
```java
@SpringBootTest
@Testcontainers
class PaymentIntegrationTest {
    
    @Container
    static PostgreSQLContainer<?> postgres = new PostgreSQLContainer<>("postgres:13")
            .withDatabaseName("testdb")
            .withUsername("test")
            .withPassword("test");
    
    @Autowired
    private PaymentService paymentService;
    
    @Autowired
    private TestEntityManager entityManager;
    
    @Test
    void shouldProcessPaymentEndToEnd() {
        // Given
        AccountProfile account = createTestAccount();
        entityManager.persistAndFlush(account);
        
        PaymentRequest request = createPaymentRequest(account.getAccountNumber());
        
        // When
        TransactionResult result = paymentService.processPayment(request);
        
        // Then
        assertThat(result.isSuccess()).isTrue();
        
        Transaction savedTransaction = entityManager.find(Transaction.class, result.getTransactionId());
        assertThat(savedTransaction).isNotNull();
        assertThat(savedTransaction.getStatus()).isEqualTo(TransactionStatus.COMPLETED);
    }
}
```

#### End-to-End Tests (10% of tests)
```java
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Testcontainers
class PaymentE2ETest {
    
    @Container
    static DockerComposeContainer<?> environment = new DockerComposeContainer<>(
            new File("src/test/resources/docker-compose-test.yml"))
            .withExposedService("postgres", 5432)
            .withExposedService("redis", 6379)
            .withExposedService("kafka", 9092);
    
    @Autowired
    private TestRestTemplate restTemplate;
    
    @Test
    void shouldProcessPaymentThroughAPI() {
        // Given
        PaymentRequest request = createValidPaymentRequest();
        
        // When
        ResponseEntity<PaymentResponse> response = restTemplate.postForEntity(
            "/api/v1/payments", request, PaymentResponse.class);
        
        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody().isSuccess()).isTrue();
    }
}
```

### Business Logic Validation Tests

#### COBOL Business Rule Validation
```java
@Test
void shouldValidateProductCodeMappingFromCOBOL() {
    // Test cases derived from STMMCG05 COBOL program
    assertThat(productCodeValidator.mapProductCode("DCP")).isEqualTo("BNT");
    assertThat(productCodeValidator.mapProductCode("EWT")).isEqualTo("EWT");
    assertThat(productCodeValidator.validateProductCode("IPS")).isTrue();
}

@Test
void shouldHandleTransactionStatusFromCOBOL() {
    // Test cases derived from COBOL status handling logic
    Transaction transaction = new Transaction();
    
    // Test 'C' status - Cancel Before Debit
    transactionStatusManager.updateTransactionStatus(transaction, "C");
    assertThat(transaction.getStatus()).isEqualTo(TransactionStatus.CANCELLED_BEFORE_DEBIT);
    
    // Test 'J' status - Cancel After Debit
    transactionStatusManager.updateTransactionStatus(transaction, "J");
    assertThat(transaction.getStatus()).isEqualTo(TransactionStatus.CANCELLED_AFTER_DEBIT);
}
```

#### Performance Tests
```java
@Test
void shouldProcessPaymentsWithinPerformanceThreshold() {
    // Given
    List<PaymentRequest> requests = createLargePaymentBatch(1000);
    
    // When
    long startTime = System.currentTimeMillis();
    List<TransactionResult> results = paymentService.processPaymentBatch(requests);
    long endTime = System.currentTimeMillis();
    
    // Then
    assertThat(results).hasSize(1000);
    assertThat(endTime - startTime).isLessThan(5000); // 5 seconds threshold
}
```

## Deployment Guidelines

### Containerization Strategy

#### Dockerfile for Payment Service
```dockerfile
FROM openjdk:21-jdk-slim

# Install required tools
RUN apt-get update && apt-get install -y curl && rm -rf /var/lib/apt/lists/*

# Create application user
RUN addgroup --system spring && adduser --system spring --ingroup spring

# Copy application
COPY target/payment-service-*.jar app.jar

# Set ownership
RUN chown spring:spring /app.jar

USER spring:spring

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8080/actuator/health || exit 1

# Expose port
EXPOSE 8080

# Start application
ENTRYPOINT ["java", "-jar", "/app.jar"]
```

#### Docker Compose for Development
```yaml
version: '3.8'
services:
  postgres:
    image: postgres:13
    environment:
      POSTGRES_DB: bizodata
      POSTGRES_USER: bizo
      POSTGRES_PASSWORD: bizo123
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"

  kafka:
    image: confluentinc/cp-kafka:latest
    environment:
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://localhost:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
    ports:
      - "9092:9092"
    depends_on:
      - zookeeper

  zookeeper:
    image: confluentinc/cp-zookeeper:latest
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    ports:
      - "2181:2181"

  account-service:
    build: ./account-service
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - SPRING_DATASOURCE_URL=****************************************
      - SPRING_REDIS_HOST=redis
    depends_on:
      - postgres
      - redis
    ports:
      - "8081:8080"

  payment-service:
    build: ./payment-service
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - SPRING_DATASOURCE_URL=****************************************
      - SPRING_REDIS_HOST=redis
      - SPRING_KAFKA_BOOTSTRAP_SERVERS=kafka:9092
    depends_on:
      - postgres
      - redis
      - kafka
    ports:
      - "8082:8080"

volumes:
  postgres_data:
```

### Kubernetes Deployment

#### Payment Service Deployment
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: payment-service
  namespace: bizo
spec:
  replicas: 3
  selector:
    matchLabels:
      app: payment-service
  template:
    metadata:
      labels:
        app: payment-service
    spec:
      containers:
      - name: payment-service
        image: bizo/payment-service:latest
        ports:
        - containerPort: 8080
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "kubernetes"
        - name: SPRING_DATASOURCE_URL
          valueFrom:
            secretKeyRef:
              name: db-secret
              key: database-url
        - name: SPRING_REDIS_HOST
          value: "redis-service"
        - name: SPRING_KAFKA_BOOTSTRAP_SERVERS
          value: "kafka-service:9092"
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 60
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /actuator/health/readiness
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
---
apiVersion: v1
kind: Service
metadata:
  name: payment-service
  namespace: bizo
spec:
  selector:
    app: payment-service
  ports:
  - port: 80
    targetPort: 8080
  type: ClusterIP
```

### Environment Configuration

#### Application Properties by Environment

**Development (application-dev.yml)**
```yaml
spring:
  datasource:
    url: *****************************************
    username: bizo
    password: bizo123
  redis:
    host: localhost
    port: 6379
  kafka:
    bootstrap-servers: localhost:9092

logging:
  level:
    com.scb.bizo: DEBUG
    org.springframework.transaction: DEBUG

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
```

**Production (application-prod.yml)**
```yaml
spring:
  datasource:
    url: ${DATABASE_URL}
    username: ${DATABASE_USERNAME}
    password: ${DATABASE_PASSWORD}
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
  redis:
    host: ${REDIS_HOST}
    port: ${REDIS_PORT}
    password: ${REDIS_PASSWORD}
  kafka:
    bootstrap-servers: ${KAFKA_BOOTSTRAP_SERVERS}
    producer:
      acks: all
      retries: 3
    consumer:
      enable-auto-commit: false

logging:
  level:
    com.scb.bizo: INFO
    org.springframework.transaction: WARN

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: when-authorized
```

### Monitoring and Observability

#### Prometheus Metrics Configuration
```java
@Component
public class PaymentMetrics {
    
    private final Counter paymentProcessedCounter;
    private final Timer paymentProcessingTimer;
    private final Gauge activeTransactionsGauge;
    
    public PaymentMetrics(MeterRegistry meterRegistry) {
        this.paymentProcessedCounter = Counter.builder("payments_processed_total")
            .description("Total number of payments processed")
            .tag("service", "payment")
            .register(meterRegistry);
            
        this.paymentProcessingTimer = Timer.builder("payment_processing_duration")
            .description("Time taken to process payments")
            .register(meterRegistry);
            
        this.activeTransactionsGauge = Gauge.builder("active_transactions")
            .description("Number of active transactions")
            .register(meterRegistry, this, PaymentMetrics::getActiveTransactionCount);
    }
    
    public void incrementPaymentProcessed(String paymentType) {
        paymentProcessedCounter.increment(Tags.of("type", paymentType));
    }
    
    public Timer.Sample startPaymentProcessingTimer() {
        return Timer.start(paymentProcessingTimer);
    }
    
    private double getActiveTransactionCount() {
        // Implementation to get active transaction count
        return 0;
    }
}
```

This comprehensive migration handbook provides detailed guidance for successfully migrating the COBOL system to Java Spring Boot while preserving all critical business logic and ensuring system reliability. The phased approach minimizes risk and ensures thorough testing at each stage.